<template>
  <view class="flex items-center justify-center">
    <svg :width="size" :height="size" :viewBox="`0 0 ${size} ${size}`">
      <!-- 背景圆环 -->
      <circle
        :cx="size/2"
        :cy="size/2"
        :r="radius"
        fill="none"
        class="stroke-gray-200"
        :stroke-width="strokeWidth"
      />
      <!-- 进度圆环 -->
      <circle
        :cx="size/2"
        :cy="size/2"
        :r="radius"
        fill="none"
        class="stroke-primary-500"
        :stroke-width="strokeWidth"
        :stroke-dasharray="circumference"
        :stroke-dashoffset="progressOffset"
        stroke-linecap="round"
        :style="`transform: rotate(-90deg); transform-origin: 50% 50%;`"
      />
      <!-- 百分比文字 -->
      <text
        :x="size/2"
        :y="size/2 + size*0.08"
        text-anchor="middle"
        class="fill-primary-500"
        :font-size="size * 0.22"
        font-weight="bold"
      >
        {{ displayValue }}%
      </text>
    </svg>
  </view>
</template>

<script>
export default {
  name: 'progress-circle',
  props: {
    value: { type: Number, default: 0 },
    size: { type: Number, default: 100 },
    strokeWidth: { type: Number, default: 8 }
  },
  computed: {
    radius() {
      return (this.size - this.strokeWidth) / 2;
    },
    circumference() {
      return 2 * Math.PI * this.radius;
    },
    progressOffset() {
      return this.circumference * (1 - this.value / 100);
    },
    displayValue() {
      return Math.round(this.value);
    }
  }
}
</script>

<style scoped>
.text-lg {
  font-size: 1.25rem;
}
</style> 