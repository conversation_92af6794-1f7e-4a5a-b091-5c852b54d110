import * as defu from 'defu';
export { defu } from 'defu';
export { default as getValue } from 'get-value';
export { default as setValue } from 'set-value';

declare function isRegexp(value: unknown): boolean;
declare function isMap(value: unknown): boolean;
declare function regExpTest(arr: (string | RegExp)[], str: string, options?: {
    exact?: boolean;
}): boolean;
declare function noop(): void;
declare function groupBy<T>(arr: T[], cb: (arg: T) => string): Record<string, T[]>;
declare function removeExt(file: string): string;
declare const defuOverrideArray: <Source extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
}, Defaults extends Array<{
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} | (number | boolean | any[] | Record<never, any> | null | undefined)>>(source: Source, ...defaults: Defaults) => defu.Defu<Source, Defaults>;

export { defuOverrideArray, groupBy, isMap, isRegexp, noop, regExpTest, removeExt };
