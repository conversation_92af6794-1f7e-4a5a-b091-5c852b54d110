{"name": "@weapp-tailwindcss/postcss", "version": "1.0.16", "description": "@weapp-tailwindcss/postcss", "author": "ice breaker <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/weapp-tailwindcss.git", "directory": "packages/postcss"}, "bugs": {"url": "https://github.com/sonofmagic/weapp-tailwindcss/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.mjs", "require": "./dist/types.js"}, "./html-transform": {"types": "./dist/html-transform.d.ts", "import": "./dist/html-transform.mjs", "require": "./dist/html-transform.js"}, "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"@weapp-core/escape": "~4.0.1", "postcss": "~8.5.6", "postcss-preset-env": "^10.2.4", "postcss-rem-to-responsive-pixel": "~6.0.2", "postcss-selector-parser": "~7.1.0", "@weapp-tailwindcss/shared": "1.0.2"}, "devDependencies": {"postcss-value-parser": "^4.2.0", "@weapp-tailwindcss/mangle": "1.0.4"}, "scripts": {"dev": "tsup --watch --sourcemap", "build": "tsup", "test": "vitest run", "test:dev": "vitest", "release": "pnpm publish", "lint": "eslint .", "lint:fix": "eslint . --fix"}}