{"name": "@ast-core/escape", "version": "1.0.1", "description": "ast escape function utils", "keywords": ["ast", "core", "escape"], "author": "SonOfMagic <<EMAIL>>", "license": "MIT", "sideEffects": false, "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "devDependencies": {}, "scripts": {"build": "unbuild", "test": "vitest run --coverage.enabled", "test:dev": "vitest", "release": "pnpm publish"}}