"use strict";var s=require("@csstools/postcss-progressive-custom-properties"),e=require("@csstools/css-color-parser"),r=require("@csstools/utilities"),t=require("@csstools/css-parser-algorithms"),o=require("@csstools/css-tokenizer");const a=/\bcolor-mix\(/i,i=/^color-mix$/i,basePlugin=s=>({postcssPlugin:"color-mix-variadic-function-arguments",Declaration(n){const l=n.value;if(!a.test(l))return;if(r.hasFallback(n))return;if(r.hasSupportsAtRuleAncestor(n,a))return;const u=o.tokenize({css:l}),c=t.replaceComponentValues(t.parseCommaSeparatedListOfComponentValues(u),(s=>{if(!t.isFunctionNode(s)||!i.test(s.getName()))return;const r=e.color(s);return r&&!r.syntaxFlags.has(e.SyntaxFlag.Experimental)&&r.syntaxFlags.has(e.SyntaxFlag.ColorMixVariadic)?e.serializeRGB(r):void 0})),p=t.stringify(c);if(p===l)return;let m=p;s?.subFeatures.displayP3&&(m=t.stringify(t.replaceComponentValues(t.parseCommaSeparatedListOfComponentValues(u),(s=>{if(!t.isFunctionNode(s)||!i.test(s.getName()))return;const r=e.color(s);return r&&!r.syntaxFlags.has(e.SyntaxFlag.Experimental)?e.colorDataFitsRGB_Gamut(r)?e.serializeRGB(r):e.serializeP3(r):void 0})))),n.cloneBefore({value:p}),s?.subFeatures.displayP3&&m!==p&&n.cloneBefore({value:m}),s?.preserve||n.remove()}});basePlugin.postcss=!0;const postcssPlugin=e=>{const r=Object.assign({enableProgressiveCustomProperties:!0,preserve:!1,subFeatures:{displayP3:!0}},e);return r.subFeatures=Object.assign({displayP3:!0},r.subFeatures),r.enableProgressiveCustomProperties&&(r.preserve||r.subFeatures.displayP3)?{postcssPlugin:"color-mix-variadic-function-arguments",plugins:[s(),basePlugin(r)]}:basePlugin(r)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
