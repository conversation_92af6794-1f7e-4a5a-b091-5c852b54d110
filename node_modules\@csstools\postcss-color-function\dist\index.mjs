import s from"@csstools/postcss-progressive-custom-properties";import{tokenize as o}from"@csstools/css-tokenizer";import{color as r,SyntaxFlag as e,serializeRGB as t}from"@csstools/css-color-parser";import{hasFallback as c,hasSupportsAtRuleAncestor as i}from"@csstools/utilities";import{replaceComponentValues as n,parseCommaSeparatedListOfComponentValues as l,isFunctionNode as a,stringify as p}from"@csstools/css-parser-algorithms";const u=/\bcolor\(/i,m=/^color$/i,basePlugin=s=>({postcssPlugin:"postcss-color-function",Declaration(f){const g=f.value;if(!u.test(g))return;if(c(f))return;if(i(f,u))return;const v=o({css:g}),x=n(l(v),(s=>{if(!a(s)||!m.test(s.getName()))return;const o=r(s);return o&&!(o.syntaxFlags.has(e.Experimental)||o.syntaxFlags.has(e.HasNoneKeywords)||o.syntaxFlags.has(e.RelativeColorSyntax))?t(o):void 0})),P=p(x);P!==g&&(f.cloneBefore({value:P}),s?.preserve||f.remove())}});basePlugin.postcss=!0;const postcssPlugin=o=>{const r=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},o);return r.enableProgressiveCustomProperties&&r.preserve?{postcssPlugin:"postcss-color-function",plugins:[s(),basePlugin(r)]}:basePlugin(r)};postcssPlugin.postcss=!0;export{postcssPlugin as default};
