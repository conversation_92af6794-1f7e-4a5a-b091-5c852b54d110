{"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "1.0.4", "main": "index.js", "keywords": ["mkdir", "directory", "make dir", "make", "dir", "recursive", "native"], "repository": {"type": "git", "url": "https://github.com/isaacs/node-mkdirp.git"}, "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "tap": {"check-coverage": true, "coverage-map": "map.js"}, "devDependencies": {"require-inject": "^1.4.4", "tap": "^14.10.7"}, "bin": "bin/cmd.js", "license": "MIT", "engines": {"node": ">=10"}, "files": ["bin", "lib", "index.js"]}