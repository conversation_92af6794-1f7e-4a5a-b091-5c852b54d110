const e=/\(\s*prefers-color-scheme\s*:\s*(dark|light)\s*\)/gi,s="(color: 48842621)",r="(color: 70318723)",creator=o=>{const t=Object.assign({preserve:!0},o);return{postcssPlugin:"postcss-prefers-color-scheme",prepare(){const o=new WeakSet;return{postcssPlugin:"postcss-prefers-color-scheme",AtRule(c){if(o.has(c))return;if("media"!==c.name.toLowerCase())return;const{params:a}=c,p=a.replace(e,((e,o)=>"dark"===o.toLowerCase()?s:"light"===o.toLowerCase()?r:e));a!==p&&(o.add(c),c.cloneBefore({params:p}),t.preserve||c.remove())}}}}};creator.postcss=!0;export{creator as default};
