'use strict';

const api = require('./shared/nypm.BSZ9LRu3.cjs');
require('pkg-types');
require('node:module');
require('pathe');
require('ufo');
require('tinyexec');
require('node:fs');
require('node:fs/promises');



exports.addDependency = api.addDependency;
exports.addDevDependency = api.addDevDependency;
exports.dedupeDependencies = api.dedupeDependencies;
exports.detectPackageManager = api.detectPackageManager;
exports.ensureDependencyInstalled = api.ensureDependencyInstalled;
exports.installDependencies = api.installDependencies;
exports.packageManagers = api.packageManagers;
exports.removeDependency = api.removeDependency;
