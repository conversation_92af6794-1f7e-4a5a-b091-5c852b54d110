{"name": "c12", "version": "2.0.4", "description": "Smart Config Loader", "repository": "unjs/c12", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./update": {"import": {"types": "./dist/update.d.mts", "default": "./dist/update.mjs"}, "require": {"types": "./dist/update.d.cts", "default": "./dist/update.cjs"}}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "update.d.ts"], "scripts": {"build": "automd && unbuild", "dev": "vitest dev", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint . --fix && prettier -w src test", "prepack": "unbuild", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && vitest run --coverage && pnpm test:types", "test:types": "tsc --noEmit"}, "dependencies": {"chokidar": "^4.0.3", "confbox": "^0.1.8", "defu": "^6.1.4", "dotenv": "^16.4.7", "giget": "^1.2.4", "jiti": "^2.4.2", "mlly": "^1.7.4", "ohash": "^2.0.4", "pathe": "^2.0.3", "perfect-debounce": "^1.0.0", "pkg-types": "^1.3.1", "rc9": "^2.1.2"}, "devDependencies": {"@types/node": "^22.13.4", "@vitest/coverage-v8": "^3.0.6", "automd": "^0.3.12", "changelogen": "^0.5.7", "eslint": "^9.20.1", "eslint-config-unjs": "^0.4.2", "expect-type": "^1.1.0", "magicast": "^0.3.5", "prettier": "^3.5.1", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^3.0.6"}, "peerDependencies": {"magicast": "^0.3.5"}, "peerDependenciesMeta": {"magicast": {"optional": true}}, "packageManager": "pnpm@10.4.1"}