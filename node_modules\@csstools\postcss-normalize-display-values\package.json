{"name": "@csstools/postcss-normalize-display-values", "description": "Use two values display syntax for inner and outer display types.", "version": "4.0.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {}, "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-normalize-display-values"}, "keywords": ["block", "css", "display", "flex", "flow", "flow-root", "grid", "inline", "inline-block", "inline-flex", "inline-grid", "inline-table", "list-item", "postcss-plugin", "ruby", "ruby-base", "ruby-text", "run-in", "table", "table-caption", "table-cell"]}