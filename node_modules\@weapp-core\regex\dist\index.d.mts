type ItemOrItemArray<T> = T | T[];
type ICustomRegexp = {
    tagRegexp: RegExp;
    attrRegexp: RegExp;
    tag: string;
    attrs: ItemOrItemArray<string | RegExp>;
};
interface ICreateRegexpOptions {
    exact?: boolean;
}

declare function escapeStringRegexp(str: string): string;
declare const templateClassExactRegexp: RegExp;
declare const tagWithEitherClassAndHoverClassRegexp: RegExp;
declare function handleRegexp(reg: RegExp): string;
declare function getSourceString(input: string | RegExp): string;
declare function makePattern(arr: ItemOrItemArray<string | RegExp>): string;
declare function createTemplateHandlerMatchRegexp(tag: string | RegExp, attrs: ItemOrItemArray<string | RegExp>, options?: ICreateRegexpOptions): RegExp;
declare function createTemplateClassRegexp(attrs: ItemOrItemArray<string | RegExp>, options?: ICreateRegexpOptions): RegExp;
declare function makeCustomAttributes(entries?: [string | RegExp, ItemOrItemArray<string | RegExp>][]): ICustomRegexp[] | undefined;
declare const variableRegExp: RegExp;
declare const wxsTagRegexp: RegExp;

declare function isRegexp(value: unknown): boolean;
declare const matchAll: (regex: RegExp, str: string) => RegExpExecArray[];

export { type ICreateRegexpOptions, type ICustomRegexp, type ItemOrItemArray, createTemplateClassRegexp, createTemplateHandlerMatchRegexp, escapeStringRegexp, getSourceString, handleRegexp, isRegexp, makeCustomAttributes, makePattern, matchAll, tagWithEitherClassAndHoverClassRegexp, templateClassExactRegexp, variableRegExp, wxsTagRegexp };
