{"name": "@weapp-tailwindcss/init", "version": "1.0.2", "description": "@weapp-tailwindcss/init", "author": "ice breaker <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/weapp-tailwindcss.git", "directory": "packages/init"}, "bugs": {"url": "https://github.com/sonofmagic/weapp-tailwindcss/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"fs-extra": "^11.3.0", "npm-registry-fetch": "^18.0.2", "pathe": "^2.0.3", "@weapp-tailwindcss/logger": "1.0.1", "@weapp-tailwindcss/shared": "1.0.2"}, "scripts": {"dev": "tsup --watch --sourcemap", "build": "tsup", "test": "vitest run", "test:dev": "vitest", "release": "pnpm publish", "lint": "eslint .", "lint:fix": "eslint . --fix"}}