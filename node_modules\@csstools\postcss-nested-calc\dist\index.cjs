"use strict";var e=require("postcss-value-parser"),t=require("@csstools/utilities");const r=/calc\(/gi;const creator=s=>{const a=Object.assign({preserve:!0},s);return{postcssPlugin:"postcss-nested-calc",Declaration(s,{result:o}){if((s.value.match(r)||[]).length<2)return;if(s.variable)return;if(t.hasFallback(s))return;const c=s.value;let l;try{l=e(c)}catch{return void s.warn(o,`Failed to parse value '${c}'. Leaving the original value intact.`)}if(void 0===l)return;e.walk(l.nodes,(t=>{t.type&&"function"===t.type&&"calc"===t.value.toLowerCase()&&e.walk(t.nodes,(e=>{if(e.type&&"function"===e.type)return"calc"===e.value.toLowerCase()&&void(e.value="")}))}),!0);const n=String(l);n!==c&&(s.cloneBefore({value:n}),a.preserve||s.remove())}}};creator.postcss=!0,module.exports=creator;
