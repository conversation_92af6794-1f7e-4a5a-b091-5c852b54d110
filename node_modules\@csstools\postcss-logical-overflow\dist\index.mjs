var o;function transformAxes(o,t){const e=t?"-x":"-y",i=t?"-y":"-x",n=o.prop.toLowerCase().replace("-inline",e).replace("-block",i),s=o.value;o.parent?.some((o=>"decl"==o.type&&o.prop===n&&o.value===s))||(o.cloneBefore({prop:n,value:s}),o.remove())}!function(o){o.TopToBottom="top-to-bottom",o.BottomToTop="bottom-to-top",o.RightToLeft="right-to-left",o.LeftToRight="left-to-right"}(o||(o={}));const creator=t=>{const e=Object.assign({inlineDirection:o.LeftToRight},t);switch(e.inlineDirection){case o.LeftToRight:case o.RightToLeft:case o.TopToBottom:case o.BottomToTop:break;default:throw new Error(`[postcss-logical-viewport-units] "inlineDirection" must be one of ${Object.values(o).join(", ")}`)}const i=[o.LeftToRight,o.RightToLeft].includes(e.inlineDirection);return{postcssPlugin:"postcss-logical-overflow",Declaration:{"overflow-block":o=>transformAxes(o,i),"overflow-inline":o=>transformAxes(o,i)}}};creator.postcss=!0;export{creator as default};
