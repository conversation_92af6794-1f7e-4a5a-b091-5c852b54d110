// src/constants.ts
var CSS_LANGS_RE = /\.(css|less|sass|scss|styl|stylus|pcss|postcss|sss)(?:$|\?)/;

// src/index.ts
var cssModuleRE = new RegExp(`\\.module${CSS_LANGS_RE.source}`);
var directRequestRE = /[?&]direct\b/;
var PreprocessLang = /* @__PURE__ */ ((PreprocessLang2) => {
  PreprocessLang2["less"] = "less";
  PreprocessLang2["sass"] = "sass";
  PreprocessLang2["scss"] = "scss";
  PreprocessLang2["styl"] = "styl";
  PreprocessLang2["stylus"] = "stylus";
  return PreprocessLang2;
})(PreprocessLang || {});
var PureCssLang = /* @__PURE__ */ ((PureCssLang2) => {
  PureCssLang2["css"] = "css";
  return PureCssLang2;
})(PureCssLang || {});
var PostCssDialectLang = /* @__PURE__ */ ((PostCssDialectLang2) => {
  PostCssDialectLang2["sss"] = "sugarss";
  return PostCssDialectLang2;
})(PostCssDialectLang || {});
function isCSSRequest(request) {
  return CSS_LANGS_RE.test(request);
}
function isModuleCSSRequest(request) {
  return cssModuleRE.test(request);
}
function isDirectCSSRequest(request) {
  return CSS_LANGS_RE.test(request) && directRequestRE.test(request);
}
function isDirectRequest(request) {
  return directRequestRE.test(request);
}
export {
  CSS_LANGS_RE,
  PostCssDialectLang,
  PreprocessLang,
  PureCssLang,
  cssModuleRE,
  directRequestRE,
  isCSSRequest,
  isDirectCSSRequest,
  isDirectRequest,
  isModuleCSSRequest
};
