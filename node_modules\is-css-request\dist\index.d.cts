declare const CSS_LANGS_RE: RegExp;

declare const cssModuleRE: RegExp;
declare const directRequestRE: RegExp;
declare enum PreprocessLang {
    less = "less",
    sass = "sass",
    scss = "scss",
    styl = "styl",
    stylus = "stylus"
}
declare enum PureCssLang {
    css = "css"
}
declare enum PostCssDialectLang {
    sss = "sugarss"
}
type CssLang = keyof typeof PureCssLang | keyof typeof PreprocessLang | keyof typeof PostCssDialectLang;
declare function isCSSRequest(request: string): boolean;
declare function isModuleCSSRequest(request: string): boolean;
declare function isDirectCSSRequest(request: string): boolean;
declare function isDirectRequest(request: string): boolean;

export { CSS_LANGS_RE, type CssLang, PostCssDialectLang, PreprocessLang, PureCssLang, cssModuleRE, directRequestRE, isCSSRequest, isDirectCSSRequest, isDirectRequest, isModuleCSSRequest };
