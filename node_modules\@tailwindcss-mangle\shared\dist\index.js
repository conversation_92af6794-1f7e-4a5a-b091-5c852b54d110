// ../../node_modules/.pnpm/defu@6.1.4/node_modules/defu/dist/defu.mjs
function isPlainObject(value) {
  if (value === null || typeof value !== "object") {
    return false;
  }
  const prototype = Object.getPrototypeOf(value);
  if (prototype !== null && prototype !== Object.prototype && Object.getPrototypeOf(prototype) !== null) {
    return false;
  }
  if (Symbol.iterator in value) {
    return false;
  }
  if (Symbol.toStringTag in value) {
    return Object.prototype.toString.call(value) === "[object Module]";
  }
  return true;
}
function _defu(baseObject, defaults, namespace = ".", merger) {
  if (!isPlainObject(defaults)) {
    return _defu(baseObject, {}, namespace, merger);
  }
  const object = Object.assign({}, defaults);
  for (const key in baseObject) {
    if (key === "__proto__" || key === "constructor") {
      continue;
    }
    const value = baseObject[key];
    if (value === null || value === void 0) {
      continue;
    }
    if (merger && merger(object, key, value, namespace)) {
      continue;
    }
    if (Array.isArray(value) && Array.isArray(object[key])) {
      object[key] = [...value, ...object[key]];
    } else if (isPlainObject(value) && isPlainObject(object[key])) {
      object[key] = _defu(
        value,
        object[key],
        (namespace ? `${namespace}.` : "") + key.toString(),
        merger
      );
    } else {
      object[key] = value;
    }
  }
  return object;
}
function createDefu(merger) {
  return (...arguments_) => (
    // eslint-disable-next-line unicorn/no-array-reduce
    arguments_.reduce((p, c) => _defu(p, c, "", merger), {})
  );
}
var defu = createDefu();
var defuFn = createDefu((object, key, currentValue) => {
  if (object[key] !== void 0 && typeof currentValue === "function") {
    object[key] = currentValue(object[key]);
    return true;
  }
});
var defuArrayFn = createDefu((object, key, currentValue) => {
  if (Array.isArray(object[key]) && typeof currentValue === "function") {
    object[key] = currentValue(object[key]);
    return true;
  }
});

// src/utils.ts
var defuOverrideArray = createDefu((obj, key, value) => {
  if (Array.isArray(obj[key]) && Array.isArray(value)) {
    obj[key] = value;
    return true;
  }
});
var preserveClassNames = [
  // https://tailwindcss.com/docs/transition-timing-function start
  // https://github.com/sonofmagic/tailwindcss-mangle/issues/21
  "ease-out",
  "ease-linear",
  "ease-in",
  "ease-in-out"
  // https://tailwindcss.com/docs/transition-timing-function end
];
var preserveClassNamesMap = preserveClassNames.reduce((acc, cur) => {
  acc[cur] = true;
  return acc;
}, {});
function defaultMangleClassFilter(className) {
  if (preserveClassNamesMap[className]) {
    return false;
  }
  return /[:-]/.test(className);
}
function groupBy(arr, cb) {
  if (!Array.isArray(arr)) {
    throw new TypeError("expected an array for first argument");
  }
  if (typeof cb !== "function") {
    throw new TypeError("expected a function for second argument");
  }
  const result = {};
  for (const item of arr) {
    const bucketCategory = cb(item);
    const bucket = result[bucketCategory];
    if (Array.isArray(bucket)) {
      result[bucketCategory].push(item);
    } else {
      result[bucketCategory] = [item];
    }
  }
  return result;
}
var acceptChars = [..."abcdefghijklmnopqrstuvwxyz"];
function stripEscapeSequence(words) {
  return words.replaceAll("\\", "");
}
function isRegexp(value) {
  return Object.prototype.toString.call(value) === "[object RegExp]";
}
function isMap(value) {
  return Object.prototype.toString.call(value) === "[object Map]";
}
function regExpTest(arr = [], str) {
  if (Array.isArray(arr)) {
    for (const item of arr) {
      if (typeof item === "string") {
        if (item === str) {
          return true;
        }
      } else if (isRegexp(item)) {
        item.lastIndex = 0;
        if (item.test(str)) {
          return true;
        }
      }
    }
    return false;
  }
  throw new TypeError("paramater 'arr' should be a Array of Regexp | String !");
}

// src/classGenerator.ts
var ClassGenerator = class {
  newClassMap;
  newClassSize;
  context;
  opts;
  classPrefix;
  constructor(opts = {}) {
    this.newClassMap = {};
    this.newClassSize = 0;
    this.context = {};
    this.opts = opts;
    this.classPrefix = opts.classPrefix ?? "tw-";
  }
  defaultClassGenerate() {
    const chars = [];
    let rest = (this.newClassSize - this.newClassSize % acceptChars.length) / acceptChars.length;
    if (rest > 0) {
      while (true) {
        rest -= 1;
        const m = rest % acceptChars.length;
        const c = acceptChars[m];
        chars.push(c);
        rest -= m;
        if (rest === 0) {
          break;
        }
        rest /= acceptChars.length;
      }
    }
    const prefixIndex = this.newClassSize % acceptChars.length;
    const newClassName = `${this.classPrefix}${acceptChars[prefixIndex]}${chars.join("")}`;
    return newClassName;
  }
  ignoreClassName(className) {
    return regExpTest(this.opts.ignoreClass, className);
  }
  includeFilePath(filePath) {
    const { include } = this.opts;
    return Array.isArray(include) ? regExpTest(include, filePath) : true;
  }
  excludeFilePath(filePath) {
    const { exclude } = this.opts;
    return Array.isArray(exclude) ? regExpTest(exclude, filePath) : false;
  }
  isFileIncluded(filePath) {
    return this.includeFilePath(filePath) && !this.excludeFilePath(filePath);
  }
  transformCssClass(className) {
    const key = stripEscapeSequence(className);
    const cn = this.newClassMap[key];
    if (cn) {
      return cn.name;
    }
    return className;
  }
  generateClassName(original) {
    const opts = this.opts;
    original = stripEscapeSequence(original);
    const cn = this.newClassMap[original];
    if (cn) {
      return cn;
    }
    let newClassName;
    if (opts.customGenerate && typeof opts.customGenerate === "function") {
      newClassName = opts.customGenerate(original, opts, this.context);
    }
    if (!newClassName) {
      newClassName = this.defaultClassGenerate();
    }
    if (opts.reserveClassName && regExpTest(opts.reserveClassName, newClassName)) {
      if (opts.log) {
        console.log(`The class name has been reserved. ${newClassName}`);
      }
      this.newClassSize++;
      return this.generateClassName(original);
    }
    if (opts.log) {
      console.log(`Minify class name from ${original} to ${newClassName}`);
    }
    const newClass = {
      name: newClassName,
      usedBy: /* @__PURE__ */ new Set()
    };
    this.newClassMap[original] = newClass;
    this.newClassSize++;
    return newClass;
  }
};

// src/regex.ts
function escapeStringRegexp(str) {
  if (typeof str !== "string") {
    throw new TypeError("Expected a string");
  }
  return str.replaceAll(/[$()*+.?[\\\]^{|}]/g, "\\$&").replaceAll("-", "\\x2d");
}
function makeRegex(str, options = {
  exact: true
}) {
  return new RegExp(`(?<=^|[\\s"])${escapeStringRegexp(str)}${options.exact ? '(?=$|[\\s"])' : ""}`, "g");
}

// src/split.ts
var validateFilterRE = /[\w\u00A0-\uFFFF%-?]/;
function isValidSelector(selector = "") {
  return validateFilterRE.test(selector);
}
function splitCode(code, options = { splitQuote: true }) {
  const regex = options.splitQuote ? /[\s"]+/ : /\s+/;
  return code.split(regex).filter((x) => isValidSelector(x));
}
export {
  ClassGenerator,
  acceptChars,
  defaultMangleClassFilter,
  defu,
  defuOverrideArray,
  escapeStringRegexp,
  groupBy,
  isMap,
  isRegexp,
  isValidSelector,
  makeRegex,
  preserveClassNames,
  regExpTest,
  splitCode,
  stripEscapeSequence,
  validateFilterRE
};
