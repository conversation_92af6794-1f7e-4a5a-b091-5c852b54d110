{"name": "npm-registry-fetch", "version": "18.0.2", "description": "Fetch-based http client for use with npm registry APIs", "main": "lib", "files": ["bin/", "lib/"], "scripts": {"eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "test": "tap", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/npm-registry-fetch.git"}, "keywords": ["npm", "registry", "fetch"], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"@npmcli/redact": "^3.0.0", "jsonparse": "^1.3.1", "make-fetch-happen": "^14.0.0", "minipass": "^7.0.2", "minipass-fetch": "^4.0.0", "minizlib": "^3.0.1", "npm-package-arg": "^12.0.0", "proc-log": "^5.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.4", "cacache": "^19.0.1", "nock": "^13.2.4", "require-inject": "^1.4.4", "ssri": "^12.0.0", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "test-ignore": "test[\\\\/](util|cache)[\\\\/]", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.4", "publish": "true"}}