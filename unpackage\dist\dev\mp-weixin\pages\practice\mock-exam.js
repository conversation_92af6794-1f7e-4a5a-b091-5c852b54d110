"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      navBarButtonStyle: {},
      navBarStyle: {},
      // 新增导航栏样式
      hasUnfinishedExam: false,
      // 需根据接口实际判断，默认改为false以展示规则设置
      hasNewHistory: false,
      // 是否有新的历史记录提示
      rules: [
        // 示例题型，实际应根据题库返回的题型和数量生成
        { type: 1, label: "单选题", total: 20, count: 10, score: 2 },
        { type: 2, label: "多选题", total: 10, count: 5, score: 3 },
        { type: 3, label: "判断题", total: 5, count: 5, score: 1 }
      ],
      passScore: 60,
      duration: 60,
      shuffleOption: true,
      showContinueModal: false,
      // 新增继续考试弹窗控制
      showContinueBanner: false
      // 新增顶部横幅提示控制
    };
  },
  computed: {
    totalScore() {
      return this.rules.reduce((sum, r) => sum + r.count * r.score, 0);
    }
  },
  methods: {
    setNavBarButtonStyle() {
      let style = {};
      const menuButton = common_vendor.index.getMenuButtonBoundingClientRect();
      style = {
        position: "absolute",
        left: menuButton.left + "px",
        top: menuButton.top + "px",
        width: menuButton.width + "px",
        height: menuButton.height + "px",
        "z-index": 20
      };
      this.navBarButtonStyle = style;
    },
    setNavBarStyle() {
      let style = {};
      const sys = common_vendor.index.getSystemInfoSync();
      common_vendor.index.getMenuButtonBoundingClientRect();
      const navTop = sys.statusBarHeight;
      style = {
        "padding-top": navTop + "px"
      };
      this.navBarStyle = style;
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 检查是否有未完成的考试
    checkUnfinishedExam() {
    },
    continueExam() {
      common_vendor.index.navigateTo({ url: "/pages/practice/do-chapter?mode=mock" });
    },
    resetExam() {
      this.hasUnfinishedExam = false;
    },
    startExam() {
      if (this.passScore > this.totalScore) {
        common_vendor.index.showToast({ title: "及格分不能超过总分", icon: "none" });
        return;
      }
      if (this.duration < 1 || !Number.isInteger(this.duration)) {
        common_vendor.index.showToast({ title: "考试时长需为正整数", icon: "none" });
        return;
      }
      common_vendor.index.navigateTo({ url: "/pages/practice/do-chapter?mode=mock" });
    },
    goHistory() {
      common_vendor.index.navigateTo({ url: "/pages/practice/mock-exam-history" });
    },
    // 打开继续考试弹窗
    openContinueModal() {
      this.showContinueModal = true;
    },
    // 关闭继续考试弹窗
    closeContinueModal() {
      this.showContinueModal = false;
    },
    // 打开顶部横幅提示
    openContinueBanner() {
      this.showContinueBanner = true;
    },
    // 关闭顶部横幅提示
    closeContinueBanner() {
      this.showContinueBanner = false;
    },
    // +/- 按钮方法
    decreaseCount(index) {
      if (this.rules[index].count > 0) {
        this.rules[index].count--;
      }
    },
    increaseCount(index) {
      if (this.rules[index].count < this.rules[index].total) {
        this.rules[index].count++;
      }
    },
    decreaseScore(index) {
      if (this.rules[index].score > 1) {
        this.rules[index].score--;
      }
    },
    increaseScore(index) {
      this.rules[index].score++;
    },
    decreasePassScore() {
      if (this.passScore > 1) {
        this.passScore--;
      }
    },
    increasePassScore() {
      if (this.passScore < this.totalScore) {
        this.passScore++;
      }
    },
    decreaseDuration() {
      if (this.duration > 1) {
        this.duration--;
      }
    },
    increaseDuration() {
      this.duration++;
    }
  },
  onLoad(options) {
    this.setNavBarButtonStyle();
    this.setNavBarStyle();
    if (this.hasUnfinishedExam) {
      this.openContinueBanner();
      this.openContinueModal();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.s($data.navBarButtonStyle),
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.s($data.navBarStyle),
    d: $data.hasUnfinishedExam
  }, $data.hasUnfinishedExam ? {
    e: common_vendor.o((...args) => $options.continueExam && $options.continueExam(...args)),
    f: common_vendor.o((...args) => $options.resetExam && $options.resetExam(...args))
  } : {
    g: common_vendor.f($data.rules, (rule, index, i0) => {
      return {
        a: common_vendor.t(rule.label),
        b: common_vendor.t(rule.total),
        c: common_vendor.o(($event) => $options.decreaseCount(index), rule.type),
        d: rule.total,
        e: rule.count,
        f: common_vendor.o(common_vendor.m(($event) => rule.count = $event.detail.value, {
          number: true
        }), rule.type),
        g: common_vendor.o(($event) => $options.increaseCount(index), rule.type),
        h: common_vendor.o(($event) => $options.decreaseScore(index), rule.type),
        i: rule.score,
        j: common_vendor.o(common_vendor.m(($event) => rule.score = $event.detail.value, {
          number: true
        }), rule.type),
        k: common_vendor.o(($event) => $options.increaseScore(index), rule.type),
        l: common_vendor.n(index < $data.rules.length - 1 ? "border-b border-gray-100" : ""),
        m: rule.type
      };
    }),
    h: common_vendor.t($options.totalScore),
    i: common_vendor.o(($event) => $options.decreasePassScore()),
    j: $options.totalScore,
    k: $data.passScore,
    l: common_vendor.o(common_vendor.m(($event) => $data.passScore = $event.detail.value, {
      number: true
    })),
    m: common_vendor.o(($event) => $options.increasePassScore()),
    n: common_vendor.o(($event) => $options.decreaseDuration()),
    o: $data.duration,
    p: common_vendor.o(common_vendor.m(($event) => $data.duration = $event.detail.value, {
      number: true
    })),
    q: common_vendor.o(($event) => $options.increaseDuration()),
    r: $data.shuffleOption,
    s: common_vendor.o((e) => $data.shuffleOption = e.detail.value),
    t: common_vendor.t($data.shuffleOption ? "开启" : "关闭"),
    v: common_vendor.o((...args) => $options.startExam && $options.startExam(...args))
  }, {
    w: common_vendor.o((...args) => $options.goHistory && $options.goHistory(...args)),
    x: $data.hasNewHistory
  }, $data.hasNewHistory ? {} : {}, {
    y: $data.showContinueModal
  }, $data.showContinueModal ? {
    z: common_vendor.o((...args) => $options.closeContinueModal && $options.closeContinueModal(...args)),
    A: common_vendor.o((...args) => $options.continueExam && $options.continueExam(...args)),
    B: common_vendor.o((...args) => $options.resetExam && $options.resetExam(...args)),
    C: common_vendor.o(() => {
    }),
    D: common_vendor.o((...args) => $options.closeContinueModal && $options.closeContinueModal(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/practice/mock-exam.js.map
