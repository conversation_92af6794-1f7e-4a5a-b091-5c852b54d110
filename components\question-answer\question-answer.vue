<template>
	<view class="flex flex-col question-answer bg-gradient-to-br from-blue-50 to-indigo-100 ">

		<!-- 头部标题区域 -->
		<view class="bg-white shadow-lg border-b border-gray-200 px-4 py-2 header-title" v-if="title">
			<view class="flex justify-between items-center">
				<view class="flex-1 text-base font-semibold text-gray-800 truncate pr-4">
					{{title}} 
				</view>
				<view class="flex items-center space-x-3">
					<template v-if="time">
						<question-answer-countdown :stopContent="`共${maxIndex+1}题，还有${notAnswerNum}题未答`"
							:isRealExam="isRealExam" :remainingTime="time" :baseSpentDuration="spentDuration"
							:answerId="answerId" @end="postClose(3)"/>
					</template>
					<view class="flex items-center text-gray-600">
						<text class="text-lg font-semibold text-primary-600">{{index+1}}</text>
						<text class="text-gray-400 mx-1">/</text>
						<text class="text-gray-500 font-medium">{{maxIndex+1}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="flex-1 relative">
			<swiper circular :duration="swiperDuration" :current="swiperCurrent" @animationfinish="swiperChange"
				class="swiper h-full">
				<swiper-item :key="index" v-for="(item, index) in swiperList">
					<scroll-view class="scroll-view h-full" v-if="item" scroll-y>
						<question-answer-swiper :question="item" :isExam="isExam" :readonly="readonly"
							@nextQuestion="nextQuestion" :isRealExam="isRealExam"
							@updateAnswerContent="updateAnswerContent($event, item)" />
					</scroll-view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 底部导航栏 -->
		<template v-if="!hideFooter">
			<view class="bg-white border-t border-gray-200 shadow-lg pb-4">
				<view class="flex items-center justify-around h-12 px-4">
					<!-- 上一题按钮 -->
					<view :class="['flex flex-col items-center justify-center flex-1 h-full transition-all duration-200',index<=0?'opacity-50 cursor-not-allowed':'cursor-pointer hover:bg-gray-50 rounded-lg']" @click="lastQuestion">
						<view class="text-lg text-gray-600 mb-1">
							<i class="fas fa-chevron-left"></i>
						</view>
						<view class="text-xs text-gray-600 font-medium">上一题</view>
					</view>

					<!-- 答题卡按钮 -->
					<view class="flex flex-col items-center justify-center flex-1 h-full cursor-pointer hover:bg-gray-50 rounded-lg transition-all duration-200" @click="toCard">
						<view class="text-lg text-blue-500 mb-1">
							<i class="fas fa-calendar-alt"></i>
						</view>
						<view class="text-xs text-gray-600 font-medium">答题卡</view>
					</view>

					<!-- 答案按钮 -->
					<!-- <view class="flex flex-col items-center justify-center flex-1 h-full hover:bg-gray-50 rounded-lg transition-all duration-200" v-if="!readonly && !isExam">
						<view class="text-lg text-green-500 mb-1">
							<i class="fas fa-unlock"></i>
						</view>
						<view class="text-xs text-gray-600 font-medium">答案</view>
					</view> -->

					<!-- 下一题按钮 -->
					<view class="flex flex-col items-center justify-center flex-1 h-full cursor-pointer hover:bg-gray-50 rounded-lg transition-all duration-200" @click="nextQuestion" :class="['flex flex-col items-center justify-center flex-1 h-full',index >= maxIndex ?'opacity-50 cursor-not-allowed':'cursor-pointer hover:bg-gray-50 rounded-lg']"
						v-if="index < maxIndex || readonly">
						<view class="text-lg text-gray-600 mb-1">
							<i class="fas fa-chevron-right"></i>
						</view>
						<view class="text-xs text-gray-600 font-medium">下一题</view>
					</view>

					<!-- 交卷按钮 -->
					<view class="flex flex-col items-center justify-center flex-1 h-full cursor-pointer hover:bg-red-50 rounded-lg transition-all duration-200" @click="submitClose" v-else>
						<view class="text-lg text-red-500 mb-1">
							<i class="fas fa-paper-plane"></i>
						</view>
						<view class="text-xs text-gray-600 font-medium">交卷</view>
					</view>
				</view>
			</view>
		</template>

	</view>
</template>

<script>
	const defaultSwiperDuration = 300;

	export default {
		name: "question-answer",
		props: {
			title: String,
			questionList: Array,
			isExam: Boolean,
			readonly: Boolean,
			answerId: String,
			defaultIndex: Number,
			time: Number,
			spentDuration: Number,
			isRealExam: Boolean,
			watermark: String,
			hideFooter: Boolean,
			isRecite: Boolean

		},
		computed: {
			maxIndex() {
				return this.questionList.length - 1;
			},
			swiperList() {

				const {
					index,
					questionList
				} = this;

				const current = index % 3;
				const arr = [];
				arr[current] = questionList[index];
				arr[current > 0 ? current - 1 : 2] = questionList[index - 1];
				arr[current < 2 ? current + 1 : 0] = questionList[index + 1];

				return arr;
			},
			notAnswerNum() {
				return this.questionList.filter(
					(item) => item.state === 0
				).length
			}
		},

		data() {
			const defaultSwiperCurrent = this.defaultIndex ? this.defaultIndex % 3 : 0;
			return {
				index: this.defaultIndex || 0,
				swiperDuration: defaultSwiperDuration,
				swiperCurrent: defaultSwiperCurrent,
				swiperIndex: defaultSwiperCurrent,
				pausing: false
			};
		},
		watch: {
			index(nv) {
				const current = nv % 3;
				this.swiperCurrent = current;
				this.swiperIndex = current;
			}
		},
		mounted() {

			uni.$on("updateIndex", (i) => {
				if (this.index !== i) {
					this.swiperDuration = 0;
					this.index = i;
					setTimeout(() => {
						this.swiperDuration = defaultSwiperDuration;
					}, 500);
				}
			});
			uni.$on("submitClose", this.submitClose);
		},
	 
		unmounted() {
			uni.$off("updateIndex");
			uni.$off("submitClose");
		},
		methods: {

			swiperChange(e) {
				let lastIndex = this.swiperIndex;
				var current = e.detail.current;
				let currentItem = this.swiperList[current];
				this.swiperCurrent = current;
				let isLoopPositive = current == 0 && lastIndex == 2;
				if (current - lastIndex == 1 || isLoopPositive) {
					if (currentItem == null) {
						this.$nextTick(() => {
							this.swiperCurrent = lastIndex;
						});
						return;
					}
					this.index++;
				}

				// 反向滑动，到上一个的时候
				// 是反向衔接
				var isLoopNegative = current == 2 && lastIndex == 0;

				if (lastIndex - current == 1 || isLoopNegative) {
					if (currentItem == null) {
						this.$nextTick(() => {
							this.swiperCurrent = lastIndex;
						});
						return;
					}

					this.index--;
				}
				this.swiperIndex = current;
			},
			nextQuestion() {
				if (this.index >= this.maxIndex) {
					return;
				}
				this.index++;
			},
			lastQuestion() {
				if (this.index <= 0) {
					return;
				}
				this.index--;
			},
			updateAnswerContent(v, item) {
				item.state = v.state;
				item.answerContent = v.answerContent;
			},
			toCard() {
				getApp().globalData.cardData = {
					questionList: this.questionList,
					index: this.index,
					isExam: this.isExam,
					readonly: this.readonly,
				};
				uni.navigateTo({
					url: "/pages/practice/card",
				});
			},
			showAnswer() {
				this.questionList[index.value].rightAnswerVisible = true;
			},
			submitClose() {
				const notAnswerNum = this.notAnswerNum;
				const isExam = this.isExam;

				const text = `${isExam ? "交卷" : "提交"}`;

				uni.showModal({
					title: "确认",
					content: notAnswerNum ?
						`您还有${notAnswerNum}道题未作答，会影响您的结果和成绩，确定要${text}吗？` : `您已全部答题完成，确定要${text}吗？`,
					success: (res) => {
						if (res.confirm) {
							this.postClose(1);
						}
					},
				});
			},
			postClose(closeType) {
				const answerId = this.answerId;
				const isRealExam = this.isRealExam;
				this
					.$reqPost(
						isRealExam ?
						"/front/edu/user-exam/close" :
						"/front/edu/user-answer/close", {
							id: answerId,
							type: closeType
						}, {
							custom: {
								loadingTitle: '提交中'
							}
						}
					)
					.then((res) => {
						const {
							success
						} = res || {};
						if (success) {
							uni.redirectTo({
								url: isRealExam ?
									`/pages/exam/result?userExamId=${answerId}` :
									"/pages/practice/result?answerId=" + answerId,
							});
						}
					})

			}

		}
	}
</script>

<style lang="scss">
	.question-answer {
	 

		height: 100%;
		flex: 1;

		.swiper {
			height: 100%;
			flex: 1;

			.scroll-view {
				height: 100%;
			}
		}

		.opacity-50 {
			opacity: 0.5;
		}

 

 

 
	}
</style>