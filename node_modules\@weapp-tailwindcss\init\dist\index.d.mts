import { PackageJson } from 'pkg-types';
import { Options } from 'npm-registry-fetch';

type FetchOptions = Options;

interface CreateContextOptions {
    cwd: string;
    pkgJsonBasename?: string;
    postcssConfigBasename?: string;
    tailwindConfigBasename?: string;
    fetchOptions?: FetchOptions;
}
declare function createContext(options: Required<CreateContextOptions>): Promise<{
    pkgJson: PackageJson;
    pkgJsonPath: string;
    cwd: string;
    versions: {
        tailwindcss: string;
        postcss: string;
        autoprefixer: string;
        'weapp-tailwindcss': string;
    };
    postcssConfigBasename: string;
    tailwindConfigBasename: string;
    readonly type: "module" | "commonjs" | undefined;
} | undefined>;
type Context = Exclude<Awaited<ReturnType<typeof createContext>>, undefined>;
declare function updatePackageJson(ctx: Context): Promise<void>;
declare function touchPostcssConfig(ctx: Context): Promise<void>;
declare function touchTailwindConfig(ctx: Context): Promise<void>;
declare function getInitDefaults(): {
    cwd: string;
    postcssConfigBasename: string;
    tailwindConfigBasename: string;
    pkgJsonBasename: string;
};
declare function init(options?: CreateContextOptions): Promise<void>;

export { type Context, type CreateContextOptions, createContext, getInitDefaults, init, touchPostcssConfig, touchTailwindConfig, updatePackageJson };
