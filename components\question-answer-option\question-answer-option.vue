<template>
	<view class="pb-2">
		<view :class="[
			'p-3 rounded-lg border transition-all duration-200',
			status === '' ? 'border-gray-300 hover:border-blue-300' : 'border-gray-300',
			status === 'answered' ? 'bg-yellow-100 border-yellow-400 shadow-sm' : '',
			status === 'correct' ? 'bg-green-100 border-green-400 shadow-sm' : '',
			status === 'wrong' ? 'bg-red-100 border-red-400 shadow-sm' : ''
		]" @click="onClick">
			<view class="flex items-center" :class="fontSizeClass">
				<view class="font-bold pr-3 text-gray-700">{{ label }}</view>

				<view class="flex-1"><mp-html :content="text" /></view>

				<view class="pl-2">
					<view v-show="status == 'correct' || status == 'wrong'" class="font-bold text-lg">
						<i :class="status == 'correct' ? 'fas fa-check text-green-600' : 'fas fa-times text-red-600'"></i>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const optionPre = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"];

	export default {
		name: "question-answer-option",
		props: {
			isExam: Boolean,
			text: String,
			rightAnswer: String,
			answerContentArray: Array,
			showResult: Boolean,
			multiple: Boolean,
			fontSizeClass: String,
			index: Number
		},
		computed: {
			label() {
				return optionPre[this.index]
			},
			status() {
				const {
					rightAnswer,
					answerContentArray,
					index,
					showResult
				} = this;


				//作答内容

				let rightAnswerArray = rightAnswer.split(",");
				const hasAnswer =
					answerContentArray.indexOf(index + "") >= 0 ||
					answerContentArray.indexOf(index) >= 0;
				const correct =
					rightAnswerArray.indexOf(index + "") >= 0 ||
					rightAnswerArray.indexOf(index) >= 0;
				if (showResult) {
					if (hasAnswer) {
						return correct ? "correct" : "wrong";
					}
					return correct ? "correct" : "";
				} else {
					return hasAnswer ? "answered" : "";
				}
			}
		},
		data() {
			return {
				// 移除styleObj，现在使用纯TailwindCSS类
			};
		},
		methods: {
			onClick() {
				//显示结果了无法作答
				if (this.showResult) {
					return;
				}
				const {
					index,
					answerContentArray,
					multiple
				} = this;
				const result = [...answerContentArray];
				if (multiple && result.indexOf(index) >= 0) {
					result.splice(index, 1);
				} else {
					if (!multiple) {
						result.length = 0;
					}
					result.push(index);
				}
			 
				this.$emit("change", result);
			}
		}
	}
</script>

<style>

</style>