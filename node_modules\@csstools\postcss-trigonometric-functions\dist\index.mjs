import{calc as s}from"@csstools/css-calc";const e=/(?<![-\w])(?:asin|acos|atan|atan2|sin|cos|tan)\(/i,creator=t=>{const o=Object.assign({preserve:!1},t);return{postcssPlugin:"postcss-trigonometric-functions",Declaration(t){if(!e.test(t.value))return;const n=s(t.value,{precision:5,toCanonicalUnits:!0});n!==t.value&&(t.cloneBefore({value:n}),o.preserve||t.remove())}}};creator.postcss=!0;export{creator as default};
