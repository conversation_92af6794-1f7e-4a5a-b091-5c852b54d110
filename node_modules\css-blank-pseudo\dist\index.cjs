"use strict";var e=require("postcss-selector-parser");const s=[" ",">","~",":","+","@","#","(",")"];function isValidReplacement(e){let n=!0;for(let t=0,o=s.length;t<o&&n;t++)e.indexOf(s[t])>-1&&(n=!1);return n}const n="js-blank-pseudo",t=":blank",creator=s=>{const o=Object.assign({preserve:!0,replaceWith:"[blank]",disablePolyfillReadyClass:!1},s),r=e().astSync(o.replaceWith);return isValidReplacement(o.replaceWith)?{postcssPlugin:"css-blank-pseudo",prepare(){const s=new WeakSet;return{postcssPlugin:"css-blank-pseudo",Rule(l,{result:a}){if(s.has(l))return;if(!l.selector.toLowerCase().includes(t))return;const i=l.selectors.flatMap((s=>{if(!s.toLowerCase().includes(t))return[s];let i;try{i=e().astSync(s)}catch(e){return l.warn(a,`Failed to parse selector : "${s}" with message: "${e instanceof Error?e.message:e}"`),[s]}if(void 0===i)return[s];let c=!1;if(i.walkPseudos((e=>{e.value.toLowerCase()===t&&(e.nodes&&e.nodes.length||(c=!0,e.replaceWith(r.clone({}))))})),!c)return[s];const d=i.clone();if(!o.disablePolyfillReadyClass){if(i.nodes?.[0]?.nodes?.length)for(let s=0;s<i.nodes[0].nodes.length;s++){const t=i.nodes[0].nodes[s];if("combinator"===t.type||e.isPseudoElement(t)){i.nodes[0].insertBefore(t,e.className({value:n}));break}if(s===i.nodes[0].nodes.length-1){i.nodes[0].append(e.className({value:n}));break}}return i.nodes?.[0]?.nodes&&(d.nodes[0].prepend(e.combinator({value:" "})),d.nodes[0].prepend(e.className({value:n}))),[i.toString(),d.toString()]}return[i.toString()]}));i.join(",")!==l.selectors.join(",")&&(s.add(l),l.cloneBefore({selectors:i}),o.preserve||l.remove())}}}}:{postcssPlugin:"css-blank-pseudo",Once(e,{result:s}){e.warn(s,`${o.replaceWith} is not a valid replacement since it can't be applied to single elements.`)}}};creator.postcss=!0,module.exports=creator;
