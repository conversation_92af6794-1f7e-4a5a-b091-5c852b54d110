{"name": "@tailwindcss-mangle/shared", "type": "module", "version": "4.1.0", "description": "The shared utils of tailwindcss-mangle", "author": "ice breaker <<EMAIL>>", "license": "MIT", "homepage": "https://mangle.icebreaker.top/", "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/tailwindcss-mangle.git", "directory": "packages/shared"}, "bugs": {"url": "https://github.com/sonofmagic/tailwindcss-mangle/issues"}, "keywords": ["tailwindcss", "patch", "core", "mangle", "shared", "utils"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"dev": "tsup --watch --sourcemap", "build": "tsup", "test": "vitest run --coverage.enabled", "test:dev": "vitest"}}