{"name": "@weapp-tailwindcss/shared", "version": "1.0.2", "description": "@weapp-tailwindcss/shared", "author": "ice breaker <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/weapp-tailwindcss.git", "directory": "packages/shared"}, "bugs": {"url": "https://github.com/sonofmagic/weapp-tailwindcss/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./extractors": {"types": "./dist/extractors.d.ts", "import": "./dist/extractors.mjs", "require": "./dist/extractors.js"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {}, "scripts": {"dev": "tsup --watch --sourcemap", "build": "tsup", "test": "vitest run", "test:dev": "vitest", "release": "pnpm publish", "lint": "eslint .", "lint:fix": "eslint . --fix"}}