import e from"postcss-value-parser";import{hasFallback as t}from"@csstools/utilities";const o=["woff","truetype","opentype","woff2","embedded-opentype","collection","svg"],creator=r=>{const s="preserve"in Object(r)&&Boolean(r?.preserve);return{postcssPlugin:"postcss-font-format-keywords",Declaration(r){if("src"!==r.prop.toLowerCase())return;if(!r.value.toLowerCase().includes("format("))return;if(t(r))return;const n=r.parent;if(!n||"atrule"!==n.type)return;if("font-face"!==n.name.toLowerCase())return;const a=e(r.value);a.walk((t=>{"function"===t.type&&"format"===t.value.toLowerCase()&&t.nodes.forEach((t=>{"word"===t.type&&o.includes(t.value.toLowerCase())&&(t.value=e.stringify({type:"string",value:t.value,quote:'"'}))}))})),a.toString()!==r.value&&(r.cloneBefore({value:a.toString()}),s||r.remove())}}};creator.postcss=!0;export{creator as default};
