<view class="h-screen bg-gray-50 flex flex-col relative"><view class="relative w-full mb-4"><view class="w-full h-20 bg-primary-500 flex items-center justify-center relative" style="{{c}}"><view style="{{a}}" bindtap="{{b}}" class="absolute left-0 top-0 flex items-center h-full pl-4"><view class="w-8 h-8 flex items-center justify-center"><view class="fas fa-arrow-left text-white text-lg"></view></view></view><view class="flex items-center"><view class="fas fa-clipboard-list text-white text-lg mr-2"></view><text class="text-white text-lg font-medium">模拟考试</text></view></view></view><view class="flex-1 px-4 pb-20"><view wx:if="{{d}}" class="bg-white rounded-lg shadow p-6 mb-4"><view class="text-center"><view class="mb-4"><view class="fas fa-clock text-orange-500 text-4xl"></view></view><text class="text-xl font-bold text-gray-800 mb-2 block">未完成考试</text><text class="text-gray-500 text-sm mb-6 block">您有一次模拟考试尚未完成，可以选择继续作答或重新开始</text><view class="space-y-3"><button class="w-full bg-primary-500 text-white py-3 rounded-lg font-medium flex items-center justify-center" bindtap="{{e}}"><view class="fas fa-play mr-2"></view>继续考试 </button><button class="w-full border border-gray-300 bg-white text-gray-600 py-3 rounded-lg font-medium flex items-center justify-center" bindtap="{{f}}"><view class="fas fa-redo mr-2"></view>重新开始 </button></view></view></view><view wx:else class="bg-white rounded-xl shadow-sm p-5"><view class="mb-6"><block wx:for="{{g}}" wx:for-item="rule" wx:key="l"><view class="mb-4 bg-gray-50 rounded-xl p-4"><view class="flex items-center justify-between mb-3"><view><text class="text-base font-medium text-gray-800">{{rule.a}}</text><text class="text-xs text-gray-500 block mt-1">题库共{{rule.b}}题</text></view></view><view class="flex items-center justify-between"><view class="flex-1 mr-4"><text class="text-sm text-gray-600 mb-2 block">出题数量</text><view class="flex items-center bg-white rounded-lg border border-gray-200 p-1"><button bindtap="{{rule.c}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-minus text-xs"></view></button><input type="number" min="0" max="{{rule.d}}" class="flex-1 h-auto px-2 py-1 text-center text-sm border-0 bg-transparent" readonly value="{{rule.e}}" bindinput="{{rule.f}}"/><button bindtap="{{rule.g}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-plus text-xs"></view></button></view></view><view class="flex-1"><text class="text-sm text-gray-600 mb-2 block">每题分值</text><view class="flex items-center bg-white rounded-lg border border-gray-200 p-1"><button bindtap="{{rule.h}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-minus text-xs"></view></button><input type="number" min="1" class="flex-1 h-auto px-2 py-1 text-center text-sm border-0 bg-transparent" readonly value="{{rule.i}}" bindinput="{{rule.j}}"/><button bindtap="{{rule.k}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-plus text-xs"></view></button></view></view></view></view></block></view><view class="space-y-4 mb-6"><view class="bg-gray-50 rounded-xl p-4"><view class="flex items-center justify-between mb-3"><text class="text-base font-medium text-gray-800">及格分数</text><text class="text-xs text-gray-500">总分: {{h}} 分</text></view><view class="flex items-center bg-white rounded-lg border border-gray-200 p-1"><button bindtap="{{i}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-minus text-xs"></view></button><input type="number" max="{{j}}" min="1" class="flex-1 h-auto px-2 py-1 text-center text-sm border-0 bg-transparent" readonly value="{{k}}" bindinput="{{l}}"/><button bindtap="{{m}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-plus text-xs"></view></button></view></view><view class="bg-gray-50 rounded-xl p-4"><text class="text-base font-medium text-gray-800 mb-3 block">考试时长</text><view class="flex items-center bg-white rounded-lg border border-gray-200 p-1"><button bindtap="{{n}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-minus text-xs"></view></button><input type="number" min="1" step="1" class="flex-1 h-auto px-2 py-1 text-center text-sm border-0 bg-transparent" readonly value="{{o}}" bindinput="{{p}}"/><text class="text-sm text-gray-500 mx-2">分钟</text><button bindtap="{{q}}" class="w-8 h-8 flex items-center justify-center text-gray-500 hoverctext-primary-500"><view class="fas fa-plus text-xs"></view></button></view></view><view class="bg-gray-50 rounded-xl p-4 flex items-center justify-between"><text class="text-base font-medium text-gray-800">选项乱序</text><view class="flex items-center"><switch checked="{{r}}" bindchange="{{s}}" color="#3b82f6" class="mr-3"/><text class="text-sm text-gray-500">{{t}}</text></view></view></view><button class="w-full bg-primary-500 text-white py-3 rounded-lg text-base font-medium flex items-center justify-center" bindtap="{{v}}"><view class="fas fa-play mr-2"></view>开始考试 </button></view></view><view class="fixed bottom-6 right-6 z-50"><button class="w-12 h-12 rounded-full bg-primary-500 text-white shadow-lg flex items-center justify-center" bindtap="{{w}}"><view class="fas fa-history text-xl"></view></button><view wx:if="{{x}}" class="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"><text class="text-white text-xs font-bold">!</text></view></view></view><view wx:if="{{y}}" class="fixed inset-0 z-50 flex items-center justify-center" bindtap="{{D}}"><view class="absolute inset-0 bg-black bg-opacity-50"></view><view class="bg-white rounded-lg p-6 mx-6 relative z-10 w-full max-w-sm shadow-lg" catchtap="{{C}}"><button bindtap="{{z}}" class="absolute top-3 right-3 w-6 h-6 flex items-center justify-center"><view class="fas fa-times text-gray-400 text-sm"></view></button><view class="text-center"><view class="mb-4"><view class="fas fa-clock text-orange-500 text-3xl"></view></view><text class="text-lg font-bold text-gray-800 block mb-2">继续上次考试</text><text class="text-gray-500 text-sm mb-6 block">检测到您有一次未完成的模拟考试，是否继续作答？</text></view><view class="space-y-3"><button class="w-full bg-primary-500 text-white py-3 rounded-lg font-medium flex items-center justify-center" bindtap="{{A}}"><view class="fas fa-play mr-2"></view>继续考试 </button><button class="w-full border border-gray-300 bg-white text-gray-600 py-3 rounded-lg font-medium flex items-center justify-center" bindtap="{{B}}"><view class="fas fa-redo mr-2"></view>重新开始 </button></view></view></view>