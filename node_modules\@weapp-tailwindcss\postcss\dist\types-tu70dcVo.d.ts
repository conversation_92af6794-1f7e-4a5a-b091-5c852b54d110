import { IMangleScopeContext } from '@weapp-tailwindcss/mangle';
import { Rule } from 'postcss';
import { Result } from 'postcss-load-config';
import { pluginOptions } from 'postcss-preset-env';
import { UserDefinedOptions } from 'postcss-rem-to-responsive-pixel';

declare function createContext(): {
    variablesScopeWeakMap: WeakMap<WeakKey, any>;
    isVariablesScope: (rule: WeakKey) => boolean;
    markVariablesScope: (rule: WeakKey) => void;
};
type IContext = ReturnType<typeof createContext>;

type InjectPreflight = () => IPropValue[];
declare function createInjectPreflight(options?: CssPreflightOptions): InjectPreflight;

type LoadedPostcssOptions = Partial<Omit<Result, 'file'>>;
type CustomRuleCallback = (node: Rule, options: Readonly<UserDefinedPostcssOptions>) => void;
interface IPropValue {
    prop: string;
    value: string;
}
type CssPreflightOptions = {
    [key: string]: string | number | boolean;
} | false;
type RequiredStyleHandlerOptions = {
    /**
     * @description 默认为 true，此时会对样式主文件，进行猜测
     */
    isMainChunk?: boolean;
    cssPreflight?: CssPreflightOptions;
    cssInjectPreflight?: InjectPreflight;
    escapeMap?: Record<string, string>;
} & Pick<UserDefinedPostcssOptions, 'cssPreflightRange' | 'cssChildCombinatorReplaceValue' | 'injectAdditionalCssVarScope' | 'cssSelectorReplacement' | 'rem2rpx'>;
interface InternalCssSelectorReplacerOptions {
    mangleContext?: IMangleScopeContext;
    escapeMap?: Record<string, string>;
}
type IStyleHandlerOptions = {
    customRuleCallback?: CustomRuleCallback;
    mangleContext?: IMangleScopeContext;
    ctx?: IContext;
    postcssOptions?: LoadedPostcssOptions;
    cssRemoveProperty?: boolean;
    cssRemoveHoverPseudoClass?: boolean;
    cssPresetEnv?: pluginOptions;
    atRules?: {
        property?: boolean;
        supports?: boolean;
        media?: boolean;
    };
    uniAppX?: boolean;
    majorVersion?: number;
} & RequiredStyleHandlerOptions;
interface UserDefinedPostcssOptions {
    cssPreflight?: CssPreflightOptions;
    cssPreflightRange?: 'all';
    cssChildCombinatorReplaceValue?: string | string[];
    cssPresetEnv?: pluginOptions;
    injectAdditionalCssVarScope?: boolean;
    cssSelectorReplacement?: {
        root?: string | string[] | false;
        universal?: string | string[] | false;
    };
    rem2rpx?: boolean | UserDefinedOptions;
    postcssOptions?: LoadedPostcssOptions;
    cssRemoveHoverPseudoClass?: boolean;
    cssRemoveProperty?: boolean;
    customRuleCallback?: CustomRuleCallback;
    uniAppX?: boolean;
}

export { type CustomRuleCallback as C, type IStyleHandlerOptions as I, type LoadedPostcssOptions as L, type RequiredStyleHandlerOptions as R, type UserDefinedPostcssOptions as U, type InternalCssSelectorReplacerOptions as a, type IPropValue as b, createInjectPreflight as c, type CssPreflightOptions as d };
