"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  CONFIG_NAME: () => CONFIG_NAME,
  defineConfig: () => defineConfig,
  getConfig: () => getConfig,
  getDefaultMangleUserConfig: () => getDefaultMangleUserConfig,
  getDefaultPatchConfig: () => getDefaultPatchConfig,
  getDefaultUserConfig: () => getDefaultUserConfig,
  initConfig: () => initConfig
});
module.exports = __toCommonJS(index_exports);

// src/config.ts
var import_c12 = require("c12");
var import_fs_extra = __toESM(require("fs-extra"), 1);
var import_pathe = __toESM(require("pathe"), 1);

// src/constants.ts
var CONFIG_NAME = "tailwindcss-mangle";

// src/defaults.ts
var import_node_process = __toESM(require("process"), 1);
var import_shared = require("@tailwindcss-mangle/shared");
var import_is_css_request = require("is-css-request");
var defaultPipelineInclude = [
  /\.(html|js|ts|jsx|tsx|vue|svelte|astro|elm|php|phtml|mdx|md)(?:$|\?)/,
  import_is_css_request.CSS_LANGS_RE
];
var defaultPipelineExclude = [];
function getDefaultPatchConfig() {
  return {
    output: {
      filename: ".tw-patch/tw-class-list.json",
      removeUniversalSelector: true,
      loose: true
    },
    tailwindcss: {}
  };
}
function getDefaultMangleUserConfig() {
  return {
    mangleClassFilter: import_shared.defaultMangleClassFilter,
    include: defaultPipelineInclude,
    exclude: defaultPipelineExclude,
    disabled: import_node_process.default.env.NODE_ENV === "development",
    classListPath: ".tw-patch/tw-class-list.json",
    classMapOutput: {
      enable: false,
      filename: ".tw-patch/tw-map-list.json",
      loose: true
    },
    preserveFunction: []
  };
}
function getDefaultUserConfig() {
  return {
    patch: getDefaultPatchConfig(),
    mangle: getDefaultMangleUserConfig()
  };
}

// src/config.ts
function getConfig(cwd) {
  return (0, import_c12.loadConfig)({
    name: CONFIG_NAME,
    defaults: {
      ...getDefaultUserConfig()
    },
    cwd
  });
}
var defineConfig = (0, import_c12.createDefineConfig)();
function initConfig(cwd) {
  return import_fs_extra.default.outputFile(
    import_pathe.default.resolve(cwd, `${CONFIG_NAME}.config.ts`),
    `import { defineConfig } from 'tailwindcss-patch'

export default defineConfig({})
`,
    "utf8"
  );
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CONFIG_NAME,
  defineConfig,
  getConfig,
  getDefaultMangleUserConfig,
  getDefaultPatchConfig,
  getDefaultUserConfig,
  initConfig
});
