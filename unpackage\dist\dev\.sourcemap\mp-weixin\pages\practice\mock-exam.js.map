{"version": 3, "file": "mock-exam.js", "sources": ["pages/practice/mock-exam.vue", "D:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJhY3RpY2UvbW9jay1leGFtLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view\r\n    class=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col relative\"\r\n  >\r\n    <!-- 顶部标题栏 -->\r\n    <view class=\"relative w-full mb-6\">\r\n      <!-- 渐变横条+圆角过渡，动态padding-top适配小程序nav-top -->\r\n      <view\r\n        class=\"w-full h-28 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-b-3xl shadow-lg flex items-center justify-center relative overflow-hidden\"\r\n        :style=\"navBarStyle\"\r\n      >\r\n        <!-- 装饰性背景图案 -->\r\n        <view class=\"absolute inset-0 opacity-10\">\r\n          <view\r\n            class=\"absolute top-4 right-8 w-16 h-16 rounded-full bg-white\"\r\n          ></view>\r\n          <view\r\n            class=\"absolute bottom-2 left-12 w-8 h-8 rounded-full bg-white\"\r\n          ></view>\r\n          <view\r\n            class=\"absolute top-8 left-1/3 w-4 h-4 rounded-full bg-white\"\r\n          ></view>\r\n        </view>\r\n\r\n        <!-- 返回按钮 -->\r\n        <view\r\n          :style=\"navBarButtonStyle\"\r\n          @click=\"goBack\"\r\n          class=\"absolute left-0 top-0 flex items-center h-full pl-4\"\r\n        >\r\n          <view\r\n            class=\"w-10 h-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center backdrop-blur-sm\"\r\n          >\r\n            <i class=\"fas fa-arrow-left text-white text-lg\"></i>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 图标+标题 -->\r\n        <view class=\"flex flex-col items-center justify-center z-10\">\r\n          <view\r\n            class=\"w-12 h-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center mb-2 backdrop-blur-sm\"\r\n          >\r\n            <i class=\"fas fa-graduation-cap text-white text-xl\"></i>\r\n          </view>\r\n          <text class=\"text-white text-xl font-bold\">模拟考试</text>\r\n          <text class=\"text-white text-sm opacity-90 mt-1\"\r\n            >智能出题 · 精准评估</text\r\n          >\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 主体内容 -->\r\n    <view class=\"flex-1 px-4 pb-20\">\r\n      <!-- 存在未完成考试的情况 -->\r\n      <view\r\n        v-if=\"hasUnfinishedExam\"\r\n        class=\"bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-4\"\r\n      >\r\n        <view class=\"flex flex-col items-center text-center\">\r\n          <!-- 动画图标 -->\r\n          <view class=\"relative mb-6\">\r\n            <view\r\n              class=\"w-24 h-24 rounded-full bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center shadow-lg\"\r\n            >\r\n              <i class=\"fas fa-clock text-orange-500 text-3xl\"></i>\r\n            </view>\r\n            <view\r\n              class=\"absolute -top-1 -right-1 w-6 h-6 rounded-full bg-red-500 flex items-center justify-center\"\r\n            >\r\n              <i class=\"fas fa-exclamation text-white text-xs\"></i>\r\n            </view>\r\n          </view>\r\n\r\n          <text class=\"text-2xl font-bold text-gray-800 mb-2\"\r\n            >发现未完成考试</text\r\n          >\r\n          <text class=\"text-gray-500 text-base mb-6 leading-relaxed\"\r\n            >您有一次模拟考试尚未完成<br />可以选择继续作答或重新开始</text\r\n          >\r\n\r\n          <!-- 操作按钮 -->\r\n          <view class=\"w-full space-y-3\">\r\n            <button\r\n              class=\"w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center\"\r\n              @click=\"continueExam\"\r\n            >\r\n              <i class=\"fas fa-play-circle mr-3 text-xl\"></i>\r\n              继续考试\r\n            </button>\r\n            <button\r\n              class=\"w-full border-2 border-gray-200 bg-white text-gray-600 py-3 rounded-xl text-base font-medium shadow-sm hover:bg-gray-50 transition-all duration-200 flex items-center justify-center\"\r\n              @click=\"resetExam\"\r\n            >\r\n              <i class=\"fas fa-redo mr-2\"></i>\r\n              重新开始\r\n            </button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <!-- 不存在未完成考试，显示规则设置 -->\r\n      <view\r\n        v-else\r\n        class=\"bg-white rounded-2xl shadow-lg border border-gray-100 p-6\"\r\n      >\r\n        <!-- 标题区域 -->\r\n        <view class=\"text-center mb-6\">\r\n          <view\r\n            class=\"w-16 h-16 rounded-full bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center mx-auto mb-3 shadow-sm\"\r\n          >\r\n            <i class=\"fas fa-cogs text-blue-500 text-2xl\"></i>\r\n          </view>\r\n          <text class=\"text-xl font-bold text-gray-800\">考试规则设置</text>\r\n          <text class=\"text-gray-500 text-sm mt-1\">自定义您的专属考试</text>\r\n        </view>\r\n\r\n        <!-- 出题规则设置 -->\r\n        <view class=\"mb-6\">\r\n          <view class=\"flex items-center mb-4\">\r\n            <i class=\"fas fa-list-alt text-blue-500 mr-2\"></i>\r\n            <text class=\"text-lg font-semibold text-gray-800\">题型配置</text>\r\n          </view>\r\n          <block v-for=\"(rule, index) in rules\" :key=\"rule.type\">\r\n            <view\r\n              class=\"mb-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 border border-gray-100 shadow-sm\"\r\n            >\r\n              <view class=\"flex items-center justify-between mb-3\">\r\n                <view class=\"flex items-center\">\r\n                  <view\r\n                    class=\"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-3\"\r\n                  >\r\n                    <text class=\"text-white text-sm font-bold\">{{\r\n                      index + 1\r\n                    }}</text>\r\n                  </view>\r\n                  <view>\r\n                    <text class=\"text-base font-semibold text-gray-800\">{{\r\n                      rule.label\r\n                    }}</text>\r\n                    <text class=\"text-xs text-gray-500 block\"\r\n                      >题库总数: {{ rule.total }} 题</text\r\n                    >\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              <view class=\"grid grid-cols-2 gap-3\">\r\n                <view class=\"bg-white rounded-lg p-3 border border-gray-200\">\r\n                  <text class=\"text-xs text-gray-500 mb-1 block\">出题数量</text>\r\n                  <view class=\"flex items-center\">\r\n                    <input\r\n                      type=\"number\"\r\n                      v-model.number=\"rule.count\"\r\n                      min=\"0\"\r\n                      :max=\"rule.total\"\r\n                      class=\"flex-1 h-auto px-2 py-1 border border-gray-300 rounded-lg text-sm font-medium\"\r\n                      placeholder=\"0\"\r\n                    />\r\n                    <text class=\"text-gray-400 text-xs ml-2\">题</text>\r\n                  </view>\r\n                </view>\r\n                <view class=\"bg-white rounded-lg p-3 border border-gray-200\">\r\n                  <text class=\"text-xs text-gray-500 mb-1 block\">每题分值</text>\r\n                  <view class=\"flex items-center\">\r\n                    <input\r\n                      type=\"number\"\r\n                      v-model.number=\"rule.score\"\r\n                      min=\"1\"\r\n                      class=\"flex-1 h-auto px-2 py-1 border border-gray-300 rounded-lg text-sm font-medium\"\r\n                      placeholder=\"1\"\r\n                    />\r\n                    <text class=\"text-gray-400 text-xs ml-2\">分</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </block>\r\n        </view>\r\n\r\n        <!-- 考试设置 -->\r\n        <view class=\"space-y-4 mb-6\">\r\n          <view class=\"flex items-center mb-3\">\r\n            <i class=\"fas fa-sliders-h text-blue-500 mr-2\"></i>\r\n            <text class=\"text-lg font-semibold text-gray-800\">考试设置</text>\r\n          </view>\r\n\r\n          <!-- 及格分数 -->\r\n          <view\r\n            class=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100\"\r\n          >\r\n            <view class=\"flex items-center justify-between mb-2\">\r\n              <text class=\"text-base font-medium text-gray-800\">及格分数</text>\r\n              <text class=\"text-sm text-green-600 font-medium\"\r\n                >总分: {{ totalScore }} 分</text\r\n              >\r\n            </view>\r\n            <input\r\n              type=\"number\"\r\n              v-model.number=\"passScore\"\r\n              :max=\"totalScore\"\r\n              min=\"1\"\r\n              class=\"w-full h-auto px-4 py-3 border border-green-200 rounded-lg text-base bg-white\"\r\n              placeholder=\"请输入及格分数\"\r\n            />\r\n          </view>\r\n\r\n          <!-- 考试时长 -->\r\n          <view\r\n            class=\"bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-4 border border-orange-100\"\r\n          >\r\n            <text class=\"text-base font-medium text-gray-800 mb-2 block\"\r\n              >考试时长</text\r\n            >\r\n            <view class=\"flex items-center\">\r\n              <input\r\n                type=\"number\"\r\n                v-model.number=\"duration\"\r\n                min=\"1\"\r\n                step=\"1\"\r\n                class=\"flex-1 h-auto px-4 py-3 border border-orange-200 rounded-lg text-base bg-white\"\r\n                placeholder=\"60\"\r\n              />\r\n              <text class=\"text-gray-500 ml-3 font-medium\">分钟</text>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 选项乱序 -->\r\n          <view\r\n            class=\"bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-100\"\r\n          >\r\n            <view class=\"flex items-center justify-between\">\r\n              <view>\r\n                <text class=\"text-base font-medium text-gray-800\"\r\n                  >选项乱序</text\r\n                >\r\n                <text class=\"text-sm text-gray-500 block mt-1\"\r\n                  >随机打乱选项顺序</text\r\n                >\r\n              </view>\r\n              <view class=\"flex items-center\">\r\n                <switch\r\n                  :checked=\"shuffleOption\"\r\n                  @change=\"(e) => (shuffleOption = e.detail.value)\"\r\n                  color=\"#8b5cf6\"\r\n                  class=\"mr-3\"\r\n                />\r\n                <text class=\"text-gray-700 font-medium\">{{\r\n                  shuffleOption ? \"开启\" : \"关闭\"\r\n                }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 开始考试按钮 -->\r\n        <button\r\n          class=\"w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center\"\r\n          @click=\"startExam\"\r\n        >\r\n          <i class=\"fas fa-rocket mr-3 text-xl\"></i>\r\n          开始考试\r\n        </button>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 右下角固定历史记录按钮 -->\r\n    <view class=\"fixed bottom-6 right-6 z-50\">\r\n      <button\r\n        class=\"w-14 h-14 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center\"\r\n        @click=\"goHistory\"\r\n      >\r\n        <i class=\"fas fa-history text-xl\"></i>\r\n      </button>\r\n      <!-- 小红点提示（可选） -->\r\n      <view\r\n        v-if=\"hasNewHistory\"\r\n        class=\"absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center\"\r\n      >\r\n        <text class=\"text-white text-xs font-bold\">!</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n  <!-- 继续考试弹窗 -->\r\n  <view\r\n    v-if=\"showContinueModal\"\r\n    class=\"fixed inset-0 z-50 flex items-center justify-center\"\r\n    @click=\"closeContinueModal\"\r\n  >\r\n    <view\r\n      class=\"absolute inset-0 bg-black bg-opacity-60 backdrop-blur-sm\"\r\n    ></view>\r\n    <view\r\n      class=\"bg-white rounded-3xl p-8 mx-6 relative z-10 w-full max-w-sm shadow-2xl border border-gray-100\"\r\n      @click.stop\r\n    >\r\n      <!-- 关闭按钮 -->\r\n      <button\r\n        @click=\"closeContinueModal\"\r\n        class=\"absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors\"\r\n      >\r\n        <i class=\"fas fa-times text-gray-500 text-sm\"></i>\r\n      </button>\r\n\r\n      <!-- 图标和标题 -->\r\n      <view class=\"text-center mb-6\">\r\n        <view\r\n          class=\"w-16 h-16 rounded-full bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center mx-auto mb-4 shadow-sm\"\r\n        >\r\n          <i class=\"fas fa-clock text-orange-500 text-2xl\"></i>\r\n        </view>\r\n        <text class=\"text-xl font-bold text-gray-800 block mb-2\"\r\n          >继续上次考试</text\r\n        >\r\n        <text class=\"text-gray-500 text-sm leading-relaxed\"\r\n          >检测到您有一次未完成的模拟考试<br />是否继续作答？</text\r\n        >\r\n      </view>\r\n\r\n      <!-- 操作按钮 -->\r\n      <view class=\"space-y-3\">\r\n        <button\r\n          class=\"w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center\"\r\n          @click=\"continueExam\"\r\n        >\r\n          <i class=\"fas fa-play-circle mr-2\"></i>继续考试\r\n        </button>\r\n        <button\r\n          class=\"w-full border-2 border-gray-200 bg-white text-gray-600 py-3 rounded-xl font-medium hover:bg-gray-50 transition-all duration-200 flex items-center justify-center\"\r\n          @click=\"resetExam\"\r\n        >\r\n          <i class=\"fas fa-redo mr-2\"></i>重新开始\r\n        </button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      navBarButtonStyle: {},\r\n      navBarStyle: {}, // 新增导航栏样式\r\n      hasUnfinishedExam: false, // 需根据接口实际判断，默认改为false以展示规则设置\r\n      hasNewHistory: false, // 是否有新的历史记录提示\r\n      rules: [\r\n        // 示例题型，实际应根据题库返回的题型和数量生成\r\n        { type: 1, label: \"单选题\", total: 20, count: 10, score: 2 },\r\n        { type: 2, label: \"多选题\", total: 10, count: 5, score: 3 },\r\n        { type: 3, label: \"判断题\", total: 5, count: 5, score: 1 },\r\n      ],\r\n      passScore: 60,\r\n      duration: 60,\r\n      shuffleOption: true,\r\n      showContinueModal: false, // 新增继续考试弹窗控制\r\n      showContinueBanner: false, // 新增顶部横幅提示控制\r\n    };\r\n  },\r\n  computed: {\r\n    totalScore() {\r\n      return this.rules.reduce((sum, r) => sum + r.count * r.score, 0);\r\n    },\r\n  },\r\n  methods: {\r\n    setNavBarButtonStyle() {\r\n      let style = {};\r\n      // #ifdef MP-WEIXIN\r\n      const menuButton = uni.getMenuButtonBoundingClientRect();\r\n      style = {\r\n        position: \"absolute\",\r\n        left: menuButton.left + \"px\",\r\n        top: menuButton.top + \"px\",\r\n        width: menuButton.width + \"px\",\r\n        height: menuButton.height + \"px\",\r\n        \"z-index\": 20,\r\n      };\r\n      // #endif\r\n      // #ifdef H5\r\n      style = {\r\n        position: \"absolute\",\r\n        left: \"16px\",\r\n        top: \"16px\",\r\n        width: \"40px\",\r\n        height: \"40px\",\r\n        \"z-index\": 20,\r\n      };\r\n      // #endif\r\n      this.navBarButtonStyle = style;\r\n    },\r\n    setNavBarStyle() {\r\n      let style = {};\r\n      // #ifdef MP-WEIXIN\r\n      const sys = uni.getSystemInfoSync();\r\n      const menuButton = uni.getMenuButtonBoundingClientRect();\r\n      const navTop = sys.statusBarHeight;\r\n      style = {\r\n        \"padding-top\": navTop + \"px\",\r\n      };\r\n      // #endif\r\n      // #ifdef H5\r\n      style = {\r\n        \"padding-top\": \"16px\",\r\n      };\r\n      // #endif\r\n      this.navBarStyle = style;\r\n    },\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    // 检查是否有未完成的考试\r\n    checkUnfinishedExam() {\r\n      // TODO: 调用接口判断\r\n      // this.hasUnfinishedExam = ...\r\n    },\r\n    continueExam() {\r\n      // 跳转到考试页面，带上考试ID\r\n      uni.navigateTo({ url: \"/pages/practice/do-chapter?mode=mock\" });\r\n    },\r\n    resetExam() {\r\n      // 清除未完成考试，重置表单\r\n      this.hasUnfinishedExam = false;\r\n      // TODO: 调用接口清除未完成考试\r\n    },\r\n    startExam() {\r\n      if (this.passScore > this.totalScore) {\r\n        uni.showToast({ title: \"及格分不能超过总分\", icon: \"none\" });\r\n        return;\r\n      }\r\n      if (this.duration < 1 || !Number.isInteger(this.duration)) {\r\n        uni.showToast({ title: \"考试时长需为正整数\", icon: \"none\" });\r\n        return;\r\n      }\r\n      // TODO: 发起考试接口，带上规则参数\r\n      // 成功后跳转到考试页面\r\n      uni.navigateTo({ url: \"/pages/practice/do-chapter?mode=mock\" });\r\n    },\r\n    goHistory() {\r\n      uni.navigateTo({ url: \"/pages/practice/mock-exam-history\" });\r\n    },\r\n    // 打开继续考试弹窗\r\n    openContinueModal() {\r\n      this.showContinueModal = true;\r\n    },\r\n    // 关闭继续考试弹窗\r\n    closeContinueModal() {\r\n      this.showContinueModal = false;\r\n    },\r\n    // 打开顶部横幅提示\r\n    openContinueBanner() {\r\n      this.showContinueBanner = true;\r\n    },\r\n    // 关闭顶部横幅提示\r\n    closeContinueBanner() {\r\n      this.showContinueBanner = false;\r\n    },\r\n  },\r\n  onLoad(options) {\r\n    this.setNavBarButtonStyle();\r\n    this.setNavBarStyle();\r\n    if (this.hasUnfinishedExam) {\r\n      this.openContinueBanner(); // 在onLoad时打开横幅提示\r\n      this.openContinueModal(); // 在onLoad时打开弹窗\r\n    }\r\n    // TODO: 根据bankId拉取题型和数量，检查未完成考试\r\n    // this.checkUnfinishedExam();\r\n  },\r\n};\r\n</script>\r\n", "import MiniProgramPage from 'E:/work/code/edu/edu-personal-uniapp/pages/practice/mock-exam.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiVA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,mBAAmB,CAAE;AAAA,MACrB,aAAa,CAAE;AAAA;AAAA,MACf,mBAAmB;AAAA;AAAA,MACnB,eAAe;AAAA;AAAA,MACf,OAAO;AAAA;AAAA,QAEL,EAAE,MAAM,GAAG,OAAO,OAAO,OAAO,IAAI,OAAO,IAAI,OAAO,EAAG;AAAA,QACzD,EAAE,MAAM,GAAG,OAAO,OAAO,OAAO,IAAI,OAAO,GAAG,OAAO,EAAG;AAAA,QACxD,EAAE,MAAM,GAAG,OAAO,OAAO,OAAO,GAAG,OAAO,GAAG,OAAO,EAAG;AAAA,MACxD;AAAA,MACD,WAAW;AAAA,MACX,UAAU;AAAA,MACV,eAAe;AAAA,MACf,mBAAmB;AAAA;AAAA,MACnB,oBAAoB;AAAA;AAAA;EAEvB;AAAA,EACD,UAAU;AAAA,IACR,aAAa;AACX,aAAO,KAAK,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;AAAA,IAChE;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,uBAAuB;AACrB,UAAI,QAAQ,CAAA;AAEZ,YAAM,aAAaA,oBAAI;AACvB,cAAQ;AAAA,QACN,UAAU;AAAA,QACV,MAAM,WAAW,OAAO;AAAA,QACxB,KAAK,WAAW,MAAM;AAAA,QACtB,OAAO,WAAW,QAAQ;AAAA,QAC1B,QAAQ,WAAW,SAAS;AAAA,QAC5B,WAAW;AAAA;AAab,WAAK,oBAAoB;AAAA,IAC1B;AAAA,IACD,iBAAiB;AACf,UAAI,QAAQ,CAAA;AAEZ,YAAM,MAAMA,oBAAI;AACGA,oBAAAA,MAAI,gCAAiC;AACxD,YAAM,SAAS,IAAI;AACnB,cAAQ;AAAA,QACN,eAAe,SAAS;AAAA;AAQ1B,WAAK,cAAc;AAAA,IACpB;AAAA,IACD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAED,sBAAsB;AAAA,IAGrB;AAAA,IACD,eAAe;AAEbA,oBAAAA,MAAI,WAAW,EAAE,KAAK,uCAAwC,CAAA;AAAA,IAC/D;AAAA,IACD,YAAY;AAEV,WAAK,oBAAoB;AAAA,IAE1B;AAAA,IACD,YAAY;AACV,UAAI,KAAK,YAAY,KAAK,YAAY;AACpCA,sBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,MACF;AACA,UAAI,KAAK,WAAW,KAAK,CAAC,OAAO,UAAU,KAAK,QAAQ,GAAG;AACzDA,sBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAK,CAAG;AAClD;AAAA,MACF;AAGAA,oBAAAA,MAAI,WAAW,EAAE,KAAK,uCAAwC,CAAA;AAAA,IAC/D;AAAA,IACD,YAAY;AACVA,oBAAAA,MAAI,WAAW,EAAE,KAAK,oCAAqC,CAAA;AAAA,IAC5D;AAAA;AAAA,IAED,oBAAoB;AAClB,WAAK,oBAAoB;AAAA,IAC1B;AAAA;AAAA,IAED,qBAAqB;AACnB,WAAK,oBAAoB;AAAA,IAC1B;AAAA;AAAA,IAED,qBAAqB;AACnB,WAAK,qBAAqB;AAAA,IAC3B;AAAA;AAAA,IAED,sBAAsB;AACpB,WAAK,qBAAqB;AAAA,IAC3B;AAAA,EACF;AAAA,EACD,OAAO,SAAS;AACd,SAAK,qBAAoB;AACzB,SAAK,eAAc;AACnB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,mBAAkB;AACvB,WAAK,kBAAiB;AAAA,IACxB;AAAA,EAGD;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChdA,GAAG,WAAW,eAAe;"}