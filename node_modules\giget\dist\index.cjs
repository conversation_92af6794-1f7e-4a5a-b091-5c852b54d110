'use strict';

const giget = require('./shared/giget.C0XVJdqO.cjs');
require('node:fs/promises');
require('node:fs');
require('tar');
require('pathe');
require('defu');
require('nypm');
require('node:stream');
require('node:child_process');
require('node:os');
require('node:util');
require('node-fetch-native/proxy');



exports.downloadTemplate = giget.downloadTemplate;
exports.registryProvider = giget.registryProvider;
exports.startShell = giget.startShell;
