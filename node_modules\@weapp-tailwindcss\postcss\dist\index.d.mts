import { I as IStyleHandlerOptions, a as InternalCssSelectorReplacerOptions } from './types-tu70dcVo.mjs';
export { d as CssPreflightOptions, C as CustomRuleCallback, b as IPropValue, L as LoadedPostcssOptions, R as RequiredStyleHandlerOptions, U as UserDefinedPostcssOptions, c as createInjectPreflight } from './types-tu70dcVo.mjs';
import postcss from 'postcss';
import '@weapp-tailwindcss/mangle';
import 'postcss-load-config';
import 'postcss-preset-env';
import 'postcss-rem-to-responsive-pixel';

declare function createStyleHandler(options: Partial<IStyleHandlerOptions>): (rawSource: string, opt?: Partial<IStyleHandlerOptions>) => Promise<postcss.Result<postcss.Document | postcss.Root>>;

declare function internalCssSelectorReplacer(selectors: string, options?: InternalCssSelectorReplacerOptions): string;

export { IStyleHandlerOptions, InternalCssSelectorReplacerOptions, createStyleHandler, internalCssSelectorReplacer };
