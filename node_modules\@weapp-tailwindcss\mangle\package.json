{"name": "@weapp-tailwindcss/mangle", "version": "1.0.4", "description": "@weapp-tailwindcss/mangle", "author": "ice breaker <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/weapp-tailwindcss.git", "directory": "packages/mangle"}, "bugs": {"url": "https://github.com/sonofmagic/weapp-tailwindcss/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.mjs", "require": "./dist/types.js"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"@tailwindcss-mangle/shared": "~4.1.0", "@weapp-core/regex": "~1.0.1", "@weapp-tailwindcss/shared": "1.0.2"}, "scripts": {"dev": "tsup --watch --sourcemap", "build": "tsup", "test": "vitest run", "test:dev": "vitest", "release": "pnpm publish", "lint": "eslint .", "lint:fix": "eslint . --fix"}}