<template>

	<view class="flex items-center mx-2" @click="stop">
		<view class="text-xl" v-if="!isRealExam"><i :class="[isStop?'fas fa-play':'fas fa-pause']"></i></view>
		<text class="text-base">{{ m }}:{{ s }}</text>
	</view>

</template>

<script>
	export default {
		name: "countdown",
		props: {
			remainingTime: Number,
			isRealExam: Boolean,
			stopContent: String,
			baseSpentDuration: Number,
			answerId: String,

		},
		watch: {
			remainingTime: {
				handler(nv) {
					if (nv) {
						this.endTime = new Date().getTime() + (nv * 1000);
						this.handleCountDown();
						this.startUpdateSpentTimer()
					}
				},
				immediate: true
			}
		},
		data() {
			return {
				//剩余秒数
				leftSecond: 0,
				//结束时间
				endTime: 0,
				// d           : 0,
				// h           : "",
				m: "0",
				s: "0",

				isStop: false,
			};
		},
		unmounted() {
			clearTimeout(this.outTimer);
			this.clearUpdateSpentTimer();
			this.updateSpentTime()
		},
		methods: {
			stop() {
				if (this.isRealExam) {
					return;
				}
				this.isStop = true;

				clearTimeout(this.outTimer);
				this.clearUpdateSpentTimer();
				this.updateSpentTime()
				uni.showModal({
					showCancel: false,
					title: '暂停中',
					confirmText: '继续做题',
					content: this.stopContent,
					success: (res) => {
						this.isStop = false;
						this.endTime = this.leftSecond * 1000 + new Date().getTime();
						this.handleCountDown();
						this.startUpdateSpentTimer()
					}
				})
			},
			handleCountDown() {
				const leftTime = this.endTime - new Date();
				this.leftSecond = parseInt(leftTime / 1000);
				if (leftTime > 0) {
					// var day = parseInt(leftTime / (1000 * 60 * 60 * 24));
					// var hours = parseInt((leftTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
					var minutes = parseInt(leftTime / 1000 / 60);
					var seconds = parseInt((leftTime % (1000 * 60)) / 1000);
					// if (hours < 10) {
					// 	hours = '0' + hours;
					// }
					if (minutes < 10) {
						minutes = '0' + minutes;
					}
					if (seconds < 10) {
						seconds = '0' + seconds;
					}
					// this.h = hours;
					this.m = minutes;
					this.s = seconds;
					// this.d = day;
					this.outTimer = setTimeout(() => {
						this.handleCountDown();
					}, 1000);
				} else {
					clearTimeout(this.outTimer);
					// this.h = '00';
					this.m = '00';
					this.s = '00';
					// this.d = 0;
					this.$emit('end');
				}
			},
			clearUpdateSpentTimer() {
				// console.log("清除更新已用时间定时器");
				//清除更新已用时间定时器
				this.updateSpentTimer && clearInterval(this.updateSpentTimer);
			},
			startUpdateSpentTimer() {
				if (this.isRealExam) {
					return;
				}

				//开启更新已用时间定时器
				this.clearUpdateSpentTimer();
				// console.log("开启更新已用时间定时器");
				this.updateSpentTimer = setInterval(() => {
					this.updateSpentTime();
				}, 3000);
			},
			updateSpentTime() {
				const {
					answerId,
					baseSpentDuration,
					remainingTime,
					leftSecond
				} = this;
				if (answerId && remainingTime >= 0 && baseSpentDuration >= 0) {

					const useDuration = remainingTime - leftSecond;
					// console.log(useDuration,baseSpentDuration)
					 

					this.$http
						.post("/front/edu/user-answer/updateTime", {
							id: this.answerId,
							spentDuration: baseSpentDuration + (useDuration < 0 ? 0 : useDuration),
						})
						.then((res) => {
							const {
								success
							} = res.data || {};
							if (!success) {
								// console.log("更新时间失败");
							}
						});
				}

			}
		}
	}
</script>

<style>

</style>