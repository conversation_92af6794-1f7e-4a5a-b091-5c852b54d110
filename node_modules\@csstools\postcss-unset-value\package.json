{"name": "@csstools/postcss-unset-value", "description": "Use the unset keyword in CSS.", "version": "4.0.0", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"postcss": "^8.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-unset-value#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-unset-value"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["inherit", "initial", "postcss-plugin", "unset"]}