"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _nullishCoalesce(lhs, rhsFn) { if (lhs != null) { return lhs; } else { return rhsFn(); } }require('./chunk-GGNOJ77I.js');

// src/core/index.ts
var _shared = require('@tailwindcss-mangle/shared');
var _regex = require('@weapp-core/regex');
var _extractors = require('@weapp-tailwindcss/shared/extractors');
function getSelf(x) {
  return x;
}
var defaultMangleContext = {
  rawOptions: false,
  runtimeSet: /* @__PURE__ */ new Set(),
  classGenerator: new (0, _shared.ClassGenerator)(),
  filter: _shared.defaultMangleClassFilter,
  cssHandler: getSelf,
  jsHandler: getSelf,
  wxmlHandler: getSelf
};
function useMangleStore() {
  const ctx = Object.assign({}, defaultMangleContext);
  function resetMangle() {
    return Object.assign(ctx, defaultMangleContext);
  }
  function handleValue(rawSource) {
    const arr = _extractors.splitCode.call(void 0, rawSource);
    for (const x of arr) {
      if (ctx.runtimeSet.has(x)) {
        rawSource = rawSource.replace(new RegExp(_regex.escapeStringRegexp.call(void 0, x)), ctx.classGenerator.generateClassName(x).name);
      }
    }
    return rawSource;
  }
  function initMangle(options) {
    ctx.rawOptions = options;
    if (options) {
      if (options === true) {
        options = {
          classGenerator: {},
          mangleClassFilter: _shared.defaultMangleClassFilter
        };
      }
      ctx.classGenerator = new (0, _shared.ClassGenerator)(options.classGenerator);
      ctx.filter = _nullishCoalesce(options.mangleClassFilter, () => ( _shared.defaultMangleClassFilter));
      ctx.jsHandler = (rawSource) => {
        return handleValue(rawSource);
      };
      ctx.cssHandler = (rawSource) => {
        return handleValue(rawSource);
      };
      ctx.wxmlHandler = (rawSource) => {
        return handleValue(rawSource);
      };
    }
  }
  function setMangleRuntimeSet(runtimeSet) {
    const newSet = /* @__PURE__ */ new Set();
    for (const c of runtimeSet) {
      if (ctx.filter(c)) {
        newSet.add(c);
      }
    }
    ctx.runtimeSet = newSet;
  }
  return {
    mangleContext: ctx,
    resetMangle,
    initMangle,
    setMangleRuntimeSet
  };
}



exports.defaultMangleContext = defaultMangleContext; exports.useMangleStore = useMangleStore;
