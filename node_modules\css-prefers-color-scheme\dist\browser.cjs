var e=/prefers-color-scheme:/i;module.exports=function prefersColorSchemeInit(t,a){a||(a={}),a={debug:!!a.debug||!1};var r="(prefers-color-scheme: dark)",i="matchMedia"in window&&window.matchMedia(r),o=i&&i.media===r,c=function mediaQueryListener(){n(i&&i.matches?"dark":"light")},n=function set(t){"dark"!==t&&"light"!==t&&(t=o&&i.matches?"dark":"light"),t!==m&&(m=t,"function"==typeof d.onChange&&d.onChange()),[].forEach.call(document.styleSheets||[],(function(r){try{var i=[];[].forEach.call(r.cssRules||[],(function(e){i.push(e)})),i.forEach((function(a){if(e.test(Object(a.media).mediaText)){var r=[].indexOf.call(a.parentStyleSheet.cssRules,a);a.parentStyleSheet.deleteRule(r)}else{var i=(Object(a.media).mediaText||"").match(/\( *(?:color|max-color): *(48842621|70318723) *\)/i);i&&i.length>1&&("dark"===t&&"48842621"===i[1]?a.media.mediaText=a.media.mediaText.replace(/\( *color: *(?:48842621) *\)/i,"(max-color: "+i[1]+")"):"light"===t&&"70318723"===i[1]?a.media.mediaText=a.media.mediaText.replace(/\( *color: *(?:70318723) *\)/i,"(max-color: "+i[1]+")"):a.media.mediaText=a.media.mediaText.replace(/\( *max-color: *(?:48842621|70318723) *\)/i,"(color: "+i[1]+")"))}}))}catch(o){a.debug&&console.error(o)}}))},d=Object.defineProperty({hasNativeSupport:o,removeListener:function removeListener(){i&&i.removeListener(c)}},"scheme",{get:function get(){return m},set:n}),m=t||(i&&i.matches?"dark":"light");return n(m),i&&("addEventListener"in i?i.addEventListener("change",c):i.addListener(c)),d};
//# sourceMappingURL=browser.cjs.map
