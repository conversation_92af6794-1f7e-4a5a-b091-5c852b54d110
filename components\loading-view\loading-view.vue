<template>
  <view v-if="show" :class="[
    'flex items-center justify-center z-50 transition-all duration-300',
    fullscreen ? 'fixed inset-0 bg-white bg-opacity-80' : 'absolute inset-0 bg-white bg-opacity-80'
  ]">
    <view :class="[
      'flex flex-col items-center justify-center rounded-lg bg-white shadow-md transition-all duration-300',
      size === 'small' ? 'p-3' : (size === 'large' ? 'p-8' : 'p-5')
    ]">
      <!-- 加载动画 -->
      <view :class="[
        'mb-3',
        size === 'small' ? 'text-2xl' : (size === 'large' ? 'text-4xl' : 'text-3xl')
      ]">
        <text class="fas fa-circle-notch fa-spin text-primary-500"></text>
      </view>
      
      <!-- 加载文字 -->
      <view v-if="text" :class="[
        'text-gray-600',
        size === 'small' ? 'text-xs' : (size === 'large' ? 'text-base' : 'text-sm')
      ]">
        <text>{{ text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'loading-view',
  props: {
    // 是否显示加载
    show: {
      type: Boolean,
      default: true
    },
    // 加载文字
    text: {
      type: String,
      default: '加载中...'
    },
    // 是否全屏
    fullscreen: {
      type: Boolean,
      default: false
    },
    // 尺寸: small, medium, large
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    }
  }
}
</script>

<style scoped>
 
</style> 