<template>
	<view>
		<view class="bg-white">
			<question-answer-parent-content :parentId="question.parentId" :class="fontSizeClasses[fontSize + 1]"
				v-if="question.parentId && question.parentId !== '0'" :questionVersion="question.questionVersion" />
		</view>
		<view class="py-1 px-4 bg-white">
			<view class="flex justify-between items-center ">
				<view class="flex items-center ">
					<view class="bg-blue-500 text-white px-2 py-1 rounded text-sm">{{questionTypeText}}</view>
					<view class="text-gray-500 text-sm ml-1" v-if="question.score">({{ question.score }}分)</view>
				</view>
				<view v-if="!isRealExam" class="text-xl cursor-pointer" @click="onCollectQuestion">
					<i :class="!question.collected?'text-gray-500 fas fa-heart':'text-yellow-500 fas fa-heart'"></i>
				</view>
			</view>
		</view>

		<view class="py-1 px-4 bg-white">
			<view :class="fontSizeClasses[fontSize + 1]"><mp-html :content="question.questionContent" /></view>
		</view>


		<view class="px-4 pt-4 bg-white pb-4">
			<question-answer-option :key="index" :text="item" :isExam="isExam" :index="index" @change="answerChange"
				:fontSizeClass="fontSizeClasses[fontSize + 1]" :showResult="showResult"
				:rightAnswer="question.rightAnswer" :answerContentArray="answerContentArray"
				:multiple="question.questionType === 2" v-for="(item, index) in options" />


			<view v-if="showSubmitBtn">
				<button @click="submitAnswer" class="w-full bg-blue-500 text-white py-3 rounded-lg text-lg">提交答题</button>
			</view>
		</view>



		<template v-if="showResult">

			<view class="bg-white mt-4">
				<view class="flex justify-between border-b border-gray-200 py-4 items-center">
					<view class="text-white" :class="
		              question.state == 2
		                ? fontSizeClasses[fontSize + 1] + ' bg-green-500'
		                : question.state == 1
		                ? fontSizeClasses[fontSize + 1] + ' bg-red-500'
		                : fontSizeClasses[fontSize + 1] + ' bg-yellow-500'
		            " style="
		              height: 50rpx;
		              line-height: 50rpx;
		              border-radius: 0 20rpx 20rpx 0;
		              padding: 0 30rpx;
		            ">
						{{
		              question.state == 2
		                ? "回答正确"
		                : question.state == 1
		                ? "回答错误"
		                : "未作答"
		            }}
					</view>

					<view class="flex items-center">
						<text class="text-sm">难度：</text>

						<view class="pr-4 text-xl flex items-center">
							<view v-for="item in 5" :key="item">
								<i
									:class="[item <= (question.difficulty==1?1: question.difficulty == 2 ? 3 : 5) ? 'fas fa-star text-yellow-500' : 'far fa-star text-gray-300']"></i>
							</view>
						</view>

					</view>
				</view>

				<view class="flex justify-between text-center items-center border-b border-gray-200 py-4">
					<view class="border-r border-gray-200 flex-1">
						<view class="text-gray-500" :class="fontSizeClasses[fontSize]">参考答案</view>
						<view class="text-green-600 font-bold my-2" :class="fontSizeClasses[fontSize + 1]">
							{{ rightAnswer }}
						</view>
					</view>
					<view class="flex-1">
						<view class="text-gray-500" :class="fontSizeClasses[fontSize]">我的答案</view>
						<view class="font-bold my-2" :class="
		                question.state == 2
		                  ? fontSizeClasses[fontSize + 1] + ' text-green-600'
		                  : question.state == 1
		                  ? fontSizeClasses[fontSize + 1] + ' text-red-600'
		                  : fontSizeClasses[fontSize + 1]
		              ">
							{{ answerContent ? answerContent : "无" }}
						</view>
					</view>
				</view>

				<view class="px-4 pb-4 py-4"  >
					<view class="flex justify-between items-center">
						<text :class="[fontSizeClasses[fontSize + 1],'text-blue-500']">[参考解析]</text>

						<!-- <view>
							<button :class="[fontSizeClasses[fontSize + 1],'bg-blue-500 text-white px-3 py-1 rounded text-sm']" @click="toCorrect">本题纠错</button>
						</view> -->
					</view>
					<view :class="[fontSizeClasses[fontSize + 1],'mt-1']">
						<mp-html :content="question.analysis" v-if="question.analysis" />
						<text v-else>本题暂无解析</text>
					</view>
				</view>
			</view>
		</template>


	</view>
</template>

<script>
	const questionTypeEnum = [{
			label: "单选题",
			value: 1
		},
		{
			label: "多选题",
			value: 2
		},
		{
			label: "判断题",
			value: 3
		},
		{
			label: "填空题",
			value: 4
		},
		{
			label: "问答题",
			value: 5
		},
		{
			label: "组合题",
			value: 9
		},

	];

	const trueFalseEnum = [{
			label: "正确",
			value: 0
		},
		{
			label: "错误",
			value: 1
		},
	];

	const fontSizeClasses = [
		"text-xs",
		"text-sm",
		"text-base",
		"text-lg",
		"text-xl",
		"text-2xl",
	];

	const optionPre = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"];

	export default {
		name: "question-answer-swiper",
		props: {
			question: Object,
			readonly: Boolean,
			isExam: Boolean,
			isRealExam: Boolean,
			isRecite: Boolean
		},
		watch: {
			question: {
				handler(newQuestion) {
					// this.question = newQuestion;

					if (newQuestion) {
						if (newQuestion.state == 0) {
							this.answerContentArray.length = 0;
						} else {
							const answerContent = newQuestion.answerContent || "";
							if (
								newQuestion.questionType == 1 ||
								newQuestion.questionType == 2 ||
								newQuestion.questionType == 3
							) {
								this.answerContentArray = answerContent
									.split(",")
									.map((item) => parseInt(item));
							}
						}
					}
				},
				immediate: true
			}

		},
		computed: {
			questionTypeText() {
				return questionTypeEnum.find(
					(item) => item.value == this.question.questionType
				)?.label;
			},
			autoShowResult() {
				return this.$store.state.qbankSetting.settingValue.autoShowResult
			},
			autoJumpNext() {
				return this.$store.state.qbankSetting.settingValue.autoJumpNext
			},
			showResult() {

				if (this.readonly || this.isRecite) {
					return true;
				}

				if (this.isExam) {
					return false;
				}
				const {
					state,
					rightAnswerVisible
				} = this.question;

				return (this.autoShowResult && state != 0) || !!rightAnswerVisible;
			},
			showSubmitBtn() {
				if (this.showResult) {
					return false;
				}
				const {
					questionType
				} = this.question || {};

				return (
					questionType == 2 &&
					this.answerContentArray &&
					this.answerContentArray.length
				);
			},
			rightAnswer() {
				const rightAnswer = this.question.rightAnswer || "";
				const rightAnswerArray = rightAnswer.split(",");
				return rightAnswerArray
					.map((item) => optionPre[parseInt(item)])
					.join();
			},
			answerContent() {
				const answerContent = this.question.answerContent || "";

				return answerContent
					.split(",")
					.map((item) => optionPre[parseInt(item)])
					.join();
			},
			options() {
				const {
					options,
					questionType
				} = this.question;
				if (questionType === 3) {
					return trueFalseEnum.map((item) => item.label);
				}
				return options;
			},
			fontSize() {
				return this.$store.state.qbankSetting.settingValue.fontSize
			}


		},
		data() {
			return {
				answerContentArray: [],
				fontSizeClasses
			};
		},
		methods: {
			answerChange(value) {
				this.answerContentArray = value;
				//单项选择的，直接提交
				const {
					questionType
				} = this.question
				if (questionType === 1 || questionType === 3) {
					this.submitAnswer();
				}
			},

			toCorrect() {
				const {
					questionId
				} = this.question;

				if (!questionId) {
					return;
				}

				uni.navigateTo({
					url: "/pages/qbank/question-correct?questionId=" + questionId
				})
			},

			onCollectQuestion() {
				const {
					collected,
					questionId
				} = this.question;
				this.$reqPost
					(
						`/front/edu/user-collection-question/${collected ? "delete" : "add"}`, {
							questionId
						}
					)
					.then((res) => {
						const {
							success
						} = res || {};
						if (success) {
							getApp().globalData.collectionQuestionReloadFlag = true;
							uni.showToast({
								title: collected ? "取消收藏" : "收藏成功"
							});
							this.question.collected = !collected;
						}
					});
			},
			submitAnswer() {
				const value = this.answerContentArray;

				const answerContent = value.sort().join();
				const state = this.question.rightAnswer == answerContent ? 2 : 1;
				this.$emit("updateAnswerContent", {
					answerContent,
					state
				});
				if (this.autoJumpNext) {
					if (this.isExam) {
						//如果是考试直接下一题
						this.$emit("nextQuestion");
					} else {
						//练习
						//作答后显示 结果的
						if (this.autoShowResult) {
							//回答正确的
							if (state == 2) {
								setTimeout(() => {
									this.$emit("nextQuestion");
								}, 500);
							}
						} else {
							//作答不显示结果的，自动下一题
							this.$emit("nextQuestion");
						}
					}
				}
				const id = this.question.id;

				if (id) {
					this.$reqPost(
							this.isRealExam ?
							"/front/edu/user-exam/saveAnswer" :
							`/front/edu/user-answer/saveAnswer`, {
								id,
								state,
								answerContent
							}
						)
						.then((res) => {
							const {
							 
								success
							} = res || {};
							if (success) {} else {
								// console.log("保存做题失败", id, state, answerContent);
							}
						});
				}
			}
		}
	}
</script>

<style>

</style>