{"name": "@weapp-tailwindcss/logger", "version": "1.0.1", "description": "@weapp-tailwindcss/logger", "author": "ice breaker <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/weapp-tailwindcss.git", "directory": "packages/logger"}, "bugs": {"url": "https://github.com/sonofmagic/weapp-tailwindcss/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"consola": "^3.4.0"}, "scripts": {"dev": "tsup --watch --sourcemap", "build": "tsup", "test": "vitest run", "test:dev": "vitest", "release": "pnpm publish", "lint": "eslint .", "lint:fix": "eslint . --fix"}}