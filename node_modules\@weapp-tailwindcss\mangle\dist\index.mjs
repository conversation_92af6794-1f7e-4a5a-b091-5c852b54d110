import "./chunk-WAXGOBY2.mjs";

// src/core/index.ts
import { ClassGenerator, defaultMangleClassFilter } from "@tailwindcss-mangle/shared";
import { escapeStringRegexp } from "@weapp-core/regex";
import { splitCode } from "@weapp-tailwindcss/shared/extractors";
function getSelf(x) {
  return x;
}
var defaultMangleContext = {
  rawOptions: false,
  runtimeSet: /* @__PURE__ */ new Set(),
  classGenerator: new ClassGenerator(),
  filter: defaultMangleClassFilter,
  cssHandler: getSelf,
  jsHandler: getSelf,
  wxmlHandler: getSelf
};
function useMangleStore() {
  const ctx = Object.assign({}, defaultMangleContext);
  function resetMangle() {
    return Object.assign(ctx, defaultMangleContext);
  }
  function handleValue(rawSource) {
    const arr = splitCode(rawSource);
    for (const x of arr) {
      if (ctx.runtimeSet.has(x)) {
        rawSource = rawSource.replace(new RegExp(escapeStringRegexp(x)), ctx.classGenerator.generateClassName(x).name);
      }
    }
    return rawSource;
  }
  function initMangle(options) {
    ctx.rawOptions = options;
    if (options) {
      if (options === true) {
        options = {
          classGenerator: {},
          mangleClassFilter: defaultMangleClassFilter
        };
      }
      ctx.classGenerator = new ClassGenerator(options.classGenerator);
      ctx.filter = options.mangleClassFilter ?? defaultMangleClassFilter;
      ctx.jsHandler = (rawSource) => {
        return handleValue(rawSource);
      };
      ctx.cssHandler = (rawSource) => {
        return handleValue(rawSource);
      };
      ctx.wxmlHandler = (rawSource) => {
        return handleValue(rawSource);
      };
    }
  }
  function setMangleRuntimeSet(runtimeSet) {
    const newSet = /* @__PURE__ */ new Set();
    for (const c of runtimeSet) {
      if (ctx.filter(c)) {
        newSet.add(c);
      }
    }
    ctx.runtimeSet = newSet;
  }
  return {
    mangleContext: ctx,
    resetMangle,
    initMangle,
    setMangleRuntimeSet
  };
}
export {
  defaultMangleContext,
  useMangleStore
};
