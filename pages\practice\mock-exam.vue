<template>
  <view class="h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col relative">
    <!-- 顶部标题栏 -->
    <view class="relative w-full mb-6">
      <!-- 渐变横条+圆角过渡，动态padding-top适配小程序nav-top -->
      <view class="w-full h-28 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-b-3xl shadow-lg flex items-center justify-center relative overflow-hidden" :style="navBarStyle">
        <!-- 装饰性背景图案 -->
        <view class="absolute inset-0 opacity-10">
          <view class="absolute top-4 right-8 w-16 h-16 rounded-full bg-white"></view>
          <view class="absolute bottom-2 left-12 w-8 h-8 rounded-full bg-white"></view>
          <view class="absolute top-8 left-1/3 w-4 h-4 rounded-full bg-white"></view>
        </view>

        <!-- 返回按钮 -->
        <view :style="navBarButtonStyle" @click="goBack" class="absolute left-0 top-0 flex items-center h-full pl-4">
          <view class="w-10 h-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center backdrop-blur-sm">
            <i class="fas fa-arrow-left text-white text-lg"></i>
          </view>
        </view>

        <!-- 图标+标题 -->
        <view class="flex flex-col items-center justify-center z-10">
          <view class="w-12 h-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center mb-2 backdrop-blur-sm">
            <i class="fas fa-graduation-cap text-white text-xl"></i>
          </view>
          <text class="text-white text-xl font-bold">模拟考试</text>
          <text class="text-white text-sm opacity-90 mt-1">智能出题 · 精准评估</text>
        </view>
      </view>
    </view>

    <!-- 主体内容 -->
    <view class="flex-1 px-4 pb-20">
      <!-- 存在未完成考试的情况 -->
      <view v-if="hasUnfinishedExam" class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-4">
        <view class="flex flex-col items-center text-center">
          <!-- 动画图标 -->
          <view class="relative mb-6">
            <view class="w-24 h-24 rounded-full bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center shadow-lg">
              <i class="fas fa-clock text-orange-500 text-3xl"></i>
            </view>
            <view class="absolute -top-1 -right-1 w-6 h-6 rounded-full bg-red-500 flex items-center justify-center">
              <i class="fas fa-exclamation text-white text-xs"></i>
            </view>
          </view>

          <text class="text-2xl font-bold text-gray-800 mb-2">发现未完成考试</text>
          <text class="text-gray-500 text-base mb-6 leading-relaxed">您有一次模拟考试尚未完成<br/>可以选择继续作答或重新开始</text>

          <!-- 操作按钮 -->
          <view class="w-full space-y-3">
            <button class="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" @click="continueExam">
              <i class="fas fa-play-circle mr-3 text-xl"></i>
              继续考试
            </button>
            <button class="w-full border-2 border-gray-200 bg-white text-gray-600 py-3 rounded-xl text-base font-medium shadow-sm hover:bg-gray-50 transition-all duration-200 flex items-center justify-center" @click="resetExam">
              <i class="fas fa-redo mr-2"></i>
              重新开始
            </button>
          </view>
        </view>
      </view>
      <!-- 不存在未完成考试，显示规则设置 -->
      <view v-else class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
        <!-- 标题区域 -->
        <view class="text-center mb-6">
          <view class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center mx-auto mb-3 shadow-sm">
            <i class="fas fa-cogs text-blue-500 text-2xl"></i>
          </view>
          <text class="text-xl font-bold text-gray-800">考试规则设置</text>
          <text class="text-gray-500 text-sm mt-1">自定义您的专属考试</text>
        </view>

        <!-- 出题规则设置 -->
        <view class="mb-6">
          <view class="flex items-center mb-4">
            <i class="fas fa-list-alt text-blue-500 mr-2"></i>
            <text class="text-lg font-semibold text-gray-800">题型配置</text>
          </view>
          <block v-for="(rule, index) in rules" :key="rule.type">
            <view class="mb-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 border border-gray-100 shadow-sm">
              <view class="flex items-center justify-between mb-3">
                <view class="flex items-center">
                  <view class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-3">
                    <text class="text-white text-sm font-bold">{{ index + 1 }}</text>
                  </view>
                  <view>
                    <text class="text-base font-semibold text-gray-800">{{ rule.label }}</text>
                    <text class="text-xs text-gray-500 block">题库总数: {{ rule.total }} 题</text>
                  </view>
                </view>
              </view>
              <view class="grid grid-cols-2 gap-3">
                <view class="bg-white rounded-lg p-3 border border-gray-200">
                  <text class="text-xs text-gray-500 mb-1 block">出题数量</text>
                  <view class="flex items-center">
                    <input type="number" v-model.number="rule.count" min="0" :max="rule.total"
                           class="flex-1 h-auto px-2 py-1 border border-gray-300 rounded-lg text-sm font-medium"
                           placeholder="0" />
                    <text class="text-gray-400 text-xs ml-2">题</text>
                  </view>
                </view>
                <view class="bg-white rounded-lg p-3 border border-gray-200">
                  <text class="text-xs text-gray-500 mb-1 block">每题分值</text>
                  <view class="flex items-center">
                    <input type="number" v-model.number="rule.score" min="1"
                           class="flex-1 h-auto px-2 py-1 border border-gray-300 rounded-lg text-sm font-medium"
                           placeholder="1" />
                    <text class="text-gray-400 text-xs ml-2">分</text>
                  </view>
                </view>
              </view>
            </view>
          </block>
        </view>

        <!-- 考试设置 -->
        <view class="space-y-4 mb-6">
          <view class="flex items-center mb-3">
            <i class="fas fa-sliders-h text-blue-500 mr-2"></i>
            <text class="text-lg font-semibold text-gray-800">考试设置</text>
          </view>

          <!-- 及格分数 -->
          <view class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100">
            <view class="flex items-center justify-between mb-2">
              <text class="text-base font-medium text-gray-800">及格分数</text>
              <text class="text-sm text-green-600 font-medium">总分: {{ totalScore }} 分</text>
            </view>
            <input type="number" v-model.number="passScore" :max="totalScore" min="1"
                   class="w-full h-auto px-4 py-3 border border-green-200 rounded-lg text-base bg-white"
                   placeholder="请输入及格分数" />
          </view>

          <!-- 考试时长 -->
          <view class="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-4 border border-orange-100">
            <text class="text-base font-medium text-gray-800 mb-2 block">考试时长</text>
            <view class="flex items-center">
              <input type="number" v-model.number="duration" min="1" step="1"
                     class="flex-1 h-auto px-4 py-3 border border-orange-200 rounded-lg text-base bg-white"
                     placeholder="60" />
              <text class="text-gray-500 ml-3 font-medium">分钟</text>
            </view>
          </view>

          <!-- 选项乱序 -->
          <view class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-100">
            <view class="flex items-center justify-between">
              <view>
                <text class="text-base font-medium text-gray-800">选项乱序</text>
                <text class="text-sm text-gray-500 block mt-1">随机打乱选项顺序</text>
              </view>
              <view class="flex items-center">
                <switch :checked="shuffleOption" @change="e => shuffleOption = e.detail.value"
                        color="#8b5cf6" class="mr-3" />
                <text class="text-gray-700 font-medium">{{ shuffleOption ? '开启' : '关闭' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 开始考试按钮 -->
        <button class="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" @click="startExam">
          <i class="fas fa-rocket mr-3 text-xl"></i>
          开始考试
        </button>
      </view>
      </view>
    </view>

    <!-- 右下角固定历史记录按钮 -->
    <view class="fixed bottom-6 right-6 z-50">
      <button
        class="w-14 h-14 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center"
        @click="goHistory"
      >
        <i class="fas fa-history text-xl"></i>
      </button>
      <!-- 小红点提示（可选） -->
      <view v-if="hasNewHistory" class="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center">
        <text class="text-white text-xs font-bold">!</text>
      </view>
    </view>
  </view>
  <!-- 继续考试弹窗 -->
  <view v-if="showContinueModal" class="fixed inset-0 z-50 flex items-center justify-center" @click="closeContinueModal">
    <view class="absolute inset-0 bg-black bg-opacity-60 backdrop-blur-sm"></view>
    <view class="bg-white rounded-3xl p-8 mx-6 relative z-10 w-full max-w-sm shadow-2xl border border-gray-100" @click.stop>
      <!-- 关闭按钮 -->
      <button @click="closeContinueModal" class="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors">
        <i class="fas fa-times text-gray-500 text-sm"></i>
      </button>

      <!-- 图标和标题 -->
      <view class="text-center mb-6">
        <view class="w-16 h-16 rounded-full bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center mx-auto mb-4 shadow-sm">
          <i class="fas fa-clock text-orange-500 text-2xl"></i>
        </view>
        <text class="text-xl font-bold text-gray-800 block mb-2">继续上次考试</text>
        <text class="text-gray-500 text-sm leading-relaxed">检测到您有一次未完成的模拟考试<br/>是否继续作答？</text>
      </view>

      <!-- 操作按钮 -->
      <view class="space-y-3">
        <button class="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" @click="continueExam">
          <i class="fas fa-play-circle mr-2"></i>继续考试
        </button>
        <button class="w-full border-2 border-gray-200 bg-white text-gray-600 py-3 rounded-xl font-medium hover:bg-gray-50 transition-all duration-200 flex items-center justify-center" @click="resetExam">
          <i class="fas fa-redo mr-2"></i>重新开始
        </button>
      </view>
    </view>
  </view>
</template>

<script>
 
export default {
  
  data() {
    return {
      navBarButtonStyle: {},
      navBarStyle: {}, // 新增导航栏样式
      hasUnfinishedExam: true, // 需根据接口实际判断
      rules: [
        // 示例题型，实际应根据题库返回的题型和数量生成
        { type: 1, label: '单选题', total: 20, count: 10, score: 2 },
        { type: 2, label: '多选题', total: 10, count: 5, score: 3 },
        { type: 3, label: '判断题', total: 5, count: 5, score: 1 },
      ],
      passScore: 60,
      duration: 60,
      shuffleOption: true,
      showContinueModal: false, // 新增继续考试弹窗控制
      showContinueBanner: false, // 新增顶部横幅提示控制
    };
  },
  computed: {
    totalScore() {
      return this.rules.reduce((sum, r) => sum + (r.count * r.score), 0);
    },
  },
  methods: {
    setNavBarButtonStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const menuButton = uni.getMenuButtonBoundingClientRect();
      style = {
        position: 'absolute',
        left: menuButton.left + 'px',
        top: menuButton.top + 'px',
        width: menuButton.width + 'px',
        height: menuButton.height + 'px',
        'z-index': 20
      };
      // #endif
      // #ifdef H5
      style = {
        position: 'absolute',
        left: '16px',
        top: '16px',
        width: '40px',
        height: '40px',
        'z-index': 20
      };
      // #endif
      this.navBarButtonStyle = style;
    },
    setNavBarStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const sys = uni.getSystemInfoSync();
      const menuButton = uni.getMenuButtonBoundingClientRect();
      const navTop = sys.statusBarHeight;
      style = {
        'padding-top': navTop + 'px',
      };
      // #endif
      // #ifdef H5
      style = {
        'padding-top': '16px',
      };
      // #endif
      this.navBarStyle = style;
    },
    goBack() {
      uni.navigateBack();
    },
    // 检查是否有未完成的考试
    checkUnfinishedExam() {
      // TODO: 调用接口判断
      // this.hasUnfinishedExam = ...
    },
    continueExam() {
      // 跳转到考试页面，带上考试ID
      uni.navigateTo({ url: '/pages/practice/do-chapter?mode=mock' });
    },
    resetExam() {
      // 清除未完成考试，重置表单
      this.hasUnfinishedExam = false;
      // TODO: 调用接口清除未完成考试
    },
    startExam() {
      if (this.passScore > this.totalScore) {
        uni.showToast({ title: '及格分不能超过总分', icon: 'none' });
        return;
      }
      if (this.duration < 1 || !Number.isInteger(this.duration)) {
        uni.showToast({ title: '考试时长需为正整数', icon: 'none' });
        return;
      }
      // TODO: 发起考试接口，带上规则参数
      // 成功后跳转到考试页面
      uni.navigateTo({ url: '/pages/practice/do-chapter?mode=mock' });
    },
    goHistory() {
      uni.navigateTo({ url: '/pages/practice/mock-exam-history' });
    },
    // 打开继续考试弹窗
    openContinueModal() {
      this.showContinueModal = true;
    },
    // 关闭继续考试弹窗
    closeContinueModal() {
      this.showContinueModal = false;
    },
    // 打开顶部横幅提示
    openContinueBanner() {
      this.showContinueBanner = true;
    },
    // 关闭顶部横幅提示
    closeContinueBanner() {
      this.showContinueBanner = false;
    },
  },
  onLoad(options) {
    this.setNavBarButtonStyle();
    this.setNavBarStyle();
    if (this.hasUnfinishedExam) {
      this.openContinueBanner(); // 在onLoad时打开横幅提示
      this.openContinueModal(); // 在onLoad时打开弹窗
    }
    // TODO: 根据bankId拉取题型和数量，检查未完成考试
    // this.checkUnfinishedExam();
  },
};
</script> 