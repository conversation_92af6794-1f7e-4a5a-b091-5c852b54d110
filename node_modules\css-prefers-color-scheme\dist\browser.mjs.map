{"version": 3, "file": "browser.mjs", "sources": ["../src/browser.js"], "sourcesContent": ["const PREFERS_COLOR_SCHEME_REGEX = /prefers-color-scheme:/i;\n\nconst prefersColorSchemeInit = (initialColorScheme, options) => {\n\t// OPTIONS\n\t{\n\t\tif (!options) {\n\t\t\toptions = {};\n\t\t}\n\n\t\toptions = {\n\t\t\tdebug: (!!options.debug) || false,\n\t\t};\n\t}\n\n\tconst mediaQueryString = '(prefers-color-scheme: dark)';\n\tconst mediaQueryList = ('matchMedia' in window) && window.matchMedia(mediaQueryString);\n\tconst hasNativeSupport = mediaQueryList && mediaQueryList.media === mediaQueryString;\n\tconst mediaQueryListener = () => {\n\t\tset((mediaQueryList && mediaQueryList.matches) ? 'dark' : 'light');\n\t};\n\tconst removeListener = () => {\n\t\tif (mediaQueryList) {\n\t\t\tmediaQueryList.removeListener(mediaQueryListener);\n\t\t}\n\t};\n\tconst set = (colorScheme) => {\n\t\tif (colorScheme !== 'dark' && colorScheme !== 'light') {\n\t\t\tif (hasNativeSupport) {\n\t\t\t\tcolorScheme = mediaQueryList.matches ? 'dark' : 'light';\n\t\t\t} else {\n\t\t\t\tcolorScheme = 'light';\n\t\t\t}\n\t\t}\n\n\t\tif (colorScheme !== currentColorScheme) {\n\t\t\tcurrentColorScheme = colorScheme;\n\n\t\t\tif (typeof result.onChange === 'function') {\n\t\t\t\tresult.onChange();\n\t\t\t}\n\t\t}\n\n\t\t[].forEach.call(document.styleSheets || [], styleSheet => {\n\t\t\ttry {\n\t\t\t\t// cssRules is a live list. Converting to an Array first.\n\t\t\t\tconst rules = [];\n\t\t\t\t[].forEach.call(styleSheet.cssRules || [], cssRule => {\n\t\t\t\t\trules.push(cssRule);\n\t\t\t\t});\n\n\t\t\t\trules.forEach(cssRule => {\n\t\t\t\t\tconst colorSchemeMatch = PREFERS_COLOR_SCHEME_REGEX.test(Object(cssRule.media).mediaText);\n\n\t\t\t\t\tif (colorSchemeMatch) {\n\t\t\t\t\t\tconst index = [].indexOf.call(cssRule.parentStyleSheet.cssRules, cssRule);\n\t\t\t\t\t\tcssRule.parentStyleSheet.deleteRule(index);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// New style which supports complex media queries.\n\t\t\t\t\t\tconst colorDepthMatch = (Object(cssRule.media).mediaText || '').match(/\\( *(?:color|max-color): *(48842621|70318723) *\\)/i);\n\t\t\t\t\t\tif (colorDepthMatch && colorDepthMatch.length > 1) {\n\t\t\t\t\t\t\tif (colorScheme === 'dark' && (colorDepthMatch[1] === '48842621')) {\n\t\t\t\t\t\t\t\t// preferred is dark and rule is dark.\n\t\t\t\t\t\t\t\tcssRule.media.mediaText = cssRule.media.mediaText.replace(/\\( *color: *(?:48842621) *\\)/i, `(max-color: ${colorDepthMatch[1]})`);\n\t\t\t\t\t\t\t} else if (colorScheme === 'light' && (colorDepthMatch[1] === '70318723')) {\n\t\t\t\t\t\t\t\t// preferred is light and rule is light.\n\t\t\t\t\t\t\t\tcssRule.media.mediaText = cssRule.media.mediaText.replace(/\\( *color: *(?:70318723) *\\)/i, `(max-color: ${colorDepthMatch[1]})`);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tcssRule.media.mediaText = cssRule.media.mediaText.replace(/\\( *max-color: *(?:48842621|70318723) *\\)/i, `(color: ${colorDepthMatch[1]})`);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} catch (e) {\n\t\t\t\tif (options.debug) {\n\t\t\t\t\tconsole.error(e);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t};\n\tconst result = Object.defineProperty(\n\t\t{ hasNativeSupport, removeListener },\n\t\t'scheme',\n\t\t{ get: () => currentColorScheme, set },\n\t);\n\n\t// initialize the color scheme using the provided value, the system value, or light\n\tlet currentColorScheme = initialColorScheme || (mediaQueryList && mediaQueryList.matches ? 'dark' : 'light');\n\n\tset(currentColorScheme);\n\n\t// listen for system changes\n\tif (mediaQueryList) {\n\t\tif ('addEventListener' in mediaQueryList) {\n\t\t\tmediaQueryList.addEventListener('change', mediaQueryListener);\n\t\t} else {\n\t\t\tmediaQueryList.addListener(mediaQueryListener);\n\t\t}\n\t}\n\n\treturn result;\n};\n\nexport default prefersColorSchemeInit;\n"], "names": ["PREFERS_COLOR_SCHEME_REGEX", "prefersColorSchemeInit", "initialColorScheme", "options", "debug", "mediaQueryString", "mediaQueryList", "window", "matchMedia", "hasNativeSupport", "media", "mediaQueryListener", "set", "matches", "colorScheme", "currentColorScheme", "result", "onChange", "for<PERSON>ach", "call", "document", "styleSheets", "styleSheet", "rules", "cssRules", "cssRule", "push", "test", "Object", "mediaText", "index", "indexOf", "parentStyleSheet", "deleteRule", "colorDepthMatch", "match", "length", "replace", "e", "console", "error", "defineProperty", "removeListener", "get", "addEventListener", "addListener"], "mappings": "AAAA,IAAMA,EAA6B,yBAE7BC,EAAyB,SAAzBA,uBAA0BC,EAAoBC,GAG7CA,IACJA,EAAU,CAAA,GAGXA,EAAU,CACTC,QAAUD,EAAQC,QAAU,GAI9B,IAAMC,EAAmB,+BACnBC,EAAkB,eAAgBC,QAAWA,OAAOC,WAAWH,GAC/DI,EAAmBH,GAAkBA,EAAeI,QAAUL,EAC9DM,EAAqB,SAArBA,qBACLC,EAAKN,GAAkBA,EAAeO,QAAW,OAAS,UAOrDD,EAAM,SAANA,IAAOE,GACQ,SAAhBA,GAA0C,UAAhBA,IAE5BA,EADGL,GACWH,EAAeO,QAAU,OAEzB,SAIZC,IAAgBC,IACnBA,EAAqBD,EAEU,mBAApBE,EAAOC,UACjBD,EAAOC,YAIT,GAAGC,QAAQC,KAAKC,SAASC,aAAe,IAAI,SAAAC,GAC3C,IAEC,IAAMC,EAAQ,GACd,GAAGL,QAAQC,KAAKG,EAAWE,UAAY,IAAI,SAAAC,GAC1CF,EAAMG,KAAKD,EACZ,IAEAF,EAAML,SAAQ,SAAAO,GAGb,GAFyBzB,EAA2B2B,KAAKC,OAAOH,EAAQf,OAAOmB,WAEzD,CACrB,IAAMC,EAAQ,GAAGC,QAAQZ,KAAKM,EAAQO,iBAAiBR,SAAUC,GACjEA,EAAQO,iBAAiBC,WAAWH,EACrC,KAAO,CAEN,IAAMI,GAAmBN,OAAOH,EAAQf,OAAOmB,WAAa,IAAIM,MAAM,sDAClED,GAAmBA,EAAgBE,OAAS,IAC3B,SAAhBtB,GAAkD,aAAvBoB,EAAgB,GAE9CT,EAAQf,MAAMmB,UAAYJ,EAAQf,MAAMmB,UAAUQ,QAAQ,gCAAgDH,eAAAA,EAAgB,QAChG,UAAhBpB,GAAmD,aAAvBoB,EAAgB,GAEtDT,EAAQf,MAAMmB,UAAYJ,EAAQf,MAAMmB,UAAUQ,QAAQ,gCAAgDH,eAAAA,EAAgB,QAE1HT,EAAQf,MAAMmB,UAAYJ,EAAQf,MAAMmB,UAAUQ,QAAQ,6CAAyDH,WAAAA,EAAgB,QAGtI,CACD,GACA,CAAC,MAAOI,GACJnC,EAAQC,OACXmC,QAAQC,MAAMF,EAEhB,CACD,KAEKtB,EAASY,OAAOa,eACrB,CAAEhC,iBAAAA,EAAkBiC,eA5DE,SAAjBA,iBACDpC,GACHA,EAAeoC,eAAe/B,KA2D/B,SACA,CAAEgC,IAAK,SAALA,MAAG,OAAQ5B,CAAkB,EAAEH,IAAAA,IAI9BG,EAAqBb,IAAuBI,GAAkBA,EAAeO,QAAU,OAAS,SAapG,OAXAD,EAAIG,GAGAT,IACC,qBAAsBA,EACzBA,EAAesC,iBAAiB,SAAUjC,GAE1CL,EAAeuC,YAAYlC,IAItBK,CACR"}