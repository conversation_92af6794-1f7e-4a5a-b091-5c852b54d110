 



function showMsg(title) {
	uni.showToast({
		title,
		icon: "none"
	});
}

function showSuccess(title) {
	uni.showToast({
		title,
		icon: "success",
		mask: true,
	});
}

function showConfirm(obj) {
	let param = {
		title: obj.title,
		content: obj.content,
		confirmText: obj.confirmText || '确定',
		cancelText: obj.cancelText || '取消',
		 
		success: (res) => {
			if (res.confirm) {
				obj.confirm && obj.confirm();
			} else {
				obj.cancel && obj.cancel();
			}
		}
	};
	 
	uni.showModal(param)
}

function showAlert(obj) {
	uni.showModal({
		title: obj.title,
		content: obj.content,
		showCancel: false,
	 
		confirmText: obj.confirmText || '确定',
		success: (res) => {
			obj.confirm && obj.confirm();
		}
	})
}

function showLoading(title, mask) {
	uni.showLoading({
		title: title || "加载中...",
		mask: mask || true
	})
}

function hideLoading() {
	uni.hideLoading()
}

export {
	showMsg,
	showAlert,
	showLoading,
	hideLoading,
	showConfirm,
	showSuccess
}