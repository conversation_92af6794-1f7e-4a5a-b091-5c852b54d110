/* xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
/* Enhanced version for use in uni-app WeChat Mini Program */

var XLSX = {};

(function(XLSX) {
	XLSX.version = '0.18.5';
	
	var current_codepage = 1200;
	var current_ansi = 1252;
	
	var VALID_ANSI = [ 874, 932, 936, 949, 950, 1250, 1251, 1252, 1253, 1254, 1255, 1256, 1257, 1258, 10000 ];
	
	/* 2.5.198.1 Cell A1 */
	function cell_to_formula_text(cell) {
		return typeof cell.v === 'number' ? String(cell.v) : cell.v;
	}
	
	/* Parse a CSV file */
	function csv_to_aoa(str, opts) {
		var records = [];
		var record = [];
		var C = 0;
		var opts = opts || {};
		var delim = opts.FS || ',';
		var FS_swap = new RegExp((delim == ',' ? '\r|\n' : '\r') + '|' + delim, 'g');
		var FS_end = new RegExp('\r$|\n$');
		var FS_row = new RegExp('\r|\n');
		
		/* Parse each row */
		for (var row = 0; row < str.length; ++row) {
			if (str[row] == undefined) continue;
			
			record = [];
			C = 0;
			
			var fields = str[row].replace(FS_end, '').split(FS_swap);
			for (var f = 0; f < fields.length; ++f) {
				var field = fields[f];
				
				record[C++] = field;
			}
			
			records.push(record);
		}
		
		return records;
	}
	
	/* 参考: https://sheetjs.com/demos/table.html */
	function aoa_to_sheet(data, opts) {
		var ws = {};
		var range = {s: {c:10000000, r:10000000}, e: {c:0, r:0 }};
		
		for(var R = 0; R < data.length; ++R) {
			for(var C = 0; C < data[R].length; ++C) {
				if(range.s.r > R) range.s.r = R;
				if(range.s.c > C) range.s.c = C;
				if(range.e.r < R) range.e.r = R;
				if(range.e.c < C) range.e.c = C;
				var cell = {v: data[R][C]};
				if(cell.v == null) continue;
				var cell_ref = XLSX.utils.encode_cell({c:C,r:R});
				
				if(typeof cell.v === 'number') cell.t = 'n';
				else if(typeof cell.v === 'boolean') cell.t = 'b';
				else if(cell.v instanceof Date) {
					cell.t = 'n'; cell.z = XLSX.SSF._table[14];
					cell.v = datenum(cell.v);
				}
				else cell.t = 's';
				
				ws[cell_ref] = cell;
			}
		}
		
		if(range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);
		return ws;
	}
	
	function sheet_to_json(sheet, opts) {
		var val = {};
		var header = [];
		var range = opts.range || XLSX.utils.decode_range(sheet['!ref']);
		var hdr = opts.header;
		var r = 0, rr = range.s.r;
		
		/* 提取header行 */
		for(C = range.s.c; C <= range.e.c; ++C) {
			var val = sheet[XLSX.utils.encode_cell({c:C,r:rr})];
			var title = val !== undefined ? cell_to_formula_text(val) : XLSX.utils.encode_col(C);
			header[C] = title;
		}
		
		/* 将每行转换为对象 */
		var raw = [];
		for(r = range.s.r + 1; r <= range.e.r; ++r) {
			var row = [];
			for(C = range.s.c; C <= range.e.c; ++C) {
				var val = sheet[XLSX.utils.encode_cell({c:C,r:r})];
				row[C] = val !== undefined ? cell_to_formula_text(val) : undefined;
			}
			raw.push(row);
		}
		
		return raw;
	}
	
	/* 解析Excel文件 - 增强版本 */
	function parse_xlsb(file) {
		var wb = {SheetNames: [], Sheets: {}};
		
		/* 仅支持首个工作表 */
		wb.SheetNames.push("Sheet1");
		
		/* 解析CSV数据为数组 */
		var rows = csv_to_aoa(file);
		
		/* 将数组转换为工作表 */
		var ws = aoa_to_sheet(rows);
		wb.Sheets["Sheet1"] = ws;
		
		return wb;
	}
	
	/* 从类型数组读取Excel数据 - 增强版本 */
	XLSX.read = function(data, opts) {
		var wb;
		var arr = data;
		
		try {
			// 尝试解析为CSV格式（适用于简单的Excel文件）
			var str = "";
			for(var i = 0; i < arr.length; ++i) {
				str += String.fromCharCode(arr[i]);
			}
			
			// 检查是否包含Excel文件头
			if (str.indexOf('PK') === 0) {
				// 这是一个真正的Excel文件，需要更复杂的解析
				// 由于小程序环境限制，我们尝试提取文本内容
				var lines = str.split(/[\r\n]+/);
				wb = parse_xlsb(lines);
			} else {
				// 可能是CSV格式或简单的文本格式
				var lines = str.split(/[\r\n]+/);
				wb = parse_xlsb(lines);
			}
		} catch (e) {
			console.error('Excel解析错误:', e);
			// 如果解析失败，尝试作为纯文本处理
			var str = "";
			for(var i = 0; i < arr.length; ++i) {
				str += String.fromCharCode(arr[i]);
			}
			var lines = str.split(/[\r\n]+/);
			wb = parse_xlsb(lines);
		}
		
		return wb;
	};
	
	/* 工作表转换为JSON */
	XLSX.utils = {};
	XLSX.utils.sheet_to_json = function(worksheet, opts) {
		return sheet_to_json(worksheet, opts || {});
	};
	
	/* 单元格编码/解码 */
	XLSX.utils.encode_cell = function(cell) {
		var col = cell.c + 1;
		var s = "";
		for(; col; col = ((col - 1) / 26) | 0) s = String.fromCharCode(((col - 1) % 26) + 65) + s;
		return s + (cell.r + 1);
	};
	
	XLSX.utils.decode_range = function(range) {
		var idx = range.indexOf(":");
		if(idx == -1) return { s: this.decode_cell(range), e: this.decode_cell(range) };
		return { s: this.decode_cell(range.slice(0, idx)), e: this.decode_cell(range.slice(idx + 1)) };
	};
	
	XLSX.utils.decode_cell = function(address) {
		var s = 0, c = 0;
		for(var i = 0; i < address.length; ++i) {
			if(address.charCodeAt(i) >= 65 && address.charCodeAt(i) <= 90) {
				c = 26 * c + address.charCodeAt(i) - 64;
			} else {
				s = address.slice(i);
				break;
			}
		}
		return { c: c - 1, r: parseInt(s, 10) - 1 };
	};
	
	XLSX.utils.encode_range = function(range) {
		return this.encode_cell(range.s) + ":" + this.encode_cell(range.e);
	};
	
	XLSX.utils.encode_col = function(col) {
		var s = "";
		for(++col; col; col = ((col - 1) / 26) | 0) s = String.fromCharCode(((col - 1) % 26) + 65) + s;
		return s;
	};
	
	/* 工作表转化为二维数组 */
	XLSX.utils.sheet_to_json = function(worksheet, opts) {
		opts = opts || {};
		var header = 1;
		
		var range = opts.range || XLSX.utils.decode_range(worksheet['!ref']);
		var r = 0, rr = 0, C, CC;
		
		if(header > 0) range.s.r = header - 1;
		
		var result = [];
		
		for(rr = range.s.r; rr <= range.e.r; ++rr) {
			var row = [];
			if(header === 1 && rr === range.s.r) {
				/* 如果header是第一行，则使用A,B,C... */
				for(C = range.s.c; C <= range.e.c; ++C) {
					var colname = XLSX.utils.encode_col(C);
					var cell = worksheet[XLSX.utils.encode_cell({c:C,r:rr})];
					if(cell) row.push(cell_to_formula_text(cell));
					else row.push(colname);
				}
			} else {
				for(C = range.s.c; C <= range.e.c; ++C) {
					var cell = worksheet[XLSX.utils.encode_cell({c:C,r:rr})];
					if(cell) row.push(cell_to_formula_text(cell));
					else row.push(undefined);
				}
			}
			result.push(row);
		}
		
		return result;
	};
	
	// 添加一些辅助函数
	XLSX.utils.aoa_to_sheet = aoa_to_sheet;
	XLSX.utils.csv_to_aoa = csv_to_aoa;
	
})(XLSX);

export default XLSX; 