const t="inline-start",o="inline-end";var e,n;function directionFlowToAxes(t){switch(t){case e.TopToBottom:return[n.Top,n.Bottom];case e.BottomToTop:return[n.Bottom,n.Top];case e.RightToLeft:return[n.Right,n.Left];case e.LeftToRight:return[n.Left,n.Right]}}function cloneDeclaration(t,o,e){t.parent&&t.parent.some((t=>"decl"==t.type&&t.prop===e&&t.value===o))||t.cloneBefore({value:o,prop:e})}!function(t){t.TopToBottom="top-to-bottom",t.BottomToTop="bottom-to-top",t.RightToLeft="right-to-left",t.LeftToRight="left-to-right"}(e||(e={})),function(t){t.Top="top",t.Right="right",t.Bottom="bottom",t.Left="left"}(n||(n={}));const creator=n=>{const i=Object.assign({inlineDirection:e.LeftToRight},n),c=Object.values(e);if(!c.includes(i.inlineDirection))throw new Error(`[postcss-logical-float-and-clear] "inlineDirection" must be one of ${c.join(", ")}`);if(![e.LeftToRight,e.RightToLeft].includes(i.inlineDirection))return{postcssPlugin:"postcss-logical-float-and-clear",Once(){}};const[r,l]=directionFlowToAxes(i.inlineDirection),makeTransform=e=>{const n=e.value.toLowerCase();if(![t,o].includes(n))return;cloneDeclaration(e,n===t?r:l,e.prop),e.remove()};return{postcssPlugin:"postcss-logical-float-and-clear",Declaration:{float:makeTransform,clear:makeTransform}}};creator.postcss=!0;export{creator as default};
