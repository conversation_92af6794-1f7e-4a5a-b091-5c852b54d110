import * as c12 from 'c12';
import { FilterPattern } from '@rollup/pluginutils';
import { IClassGeneratorOptions } from '@tailwindcss-mangle/shared';
import { SourceEntry } from '@tailwindcss/oxide';
import { PackageResolvingOptions } from 'local-pkg';

interface ClassMapOutputOptions {
    enable?: boolean;
    filename?: string;
    loose?: boolean;
}
interface ClassMapOutputItem {
    before: string;
    after: string;
    usedBy: string[];
}
interface MangleUserConfig {
    mangleClassFilter?: (className: string) => boolean;
    classGenerator?: IClassGeneratorOptions;
    exclude?: FilterPattern;
    include?: FilterPattern;
    classListPath?: string;
    classMapOutput?: boolean | ClassMapOutputOptions | ((json: ClassMapOutputItem[]) => void);
    disabled?: boolean;
    preserveFunction?: string[];
}
interface TailwindcssV2PatchConfig {
    cwd?: string;
    config?: string;
}
interface TailwindcssV3PatchConfig {
    cwd?: string;
    config?: string;
}
interface TailwindcssV4PatchConfig {
    sources?: SourceEntry[];
    base?: string;
    css?: string;
    cssEntries?: string[];
}
interface TailwindcssUserConfig {
    version?: 2 | 3 | 4;
    v2?: TailwindcssV2PatchConfig;
    v3?: TailwindcssV3PatchConfig;
    v4?: TailwindcssV4PatchConfig;
}
interface OutputUserConfig {
    filename?: string;
    loose?: boolean;
    /**
     * @description remove * in output json
     */
    removeUniversalSelector?: boolean;
}
interface PatchUserConfig {
    packageName?: string;
    output?: OutputUserConfig;
    tailwindcss?: TailwindcssUserConfig;
    resolve?: PackageResolvingOptions;
}
interface UserConfig {
    patch?: PatchUserConfig;
    mangle?: MangleUserConfig;
}

declare function getConfig(cwd?: string): Promise<c12.ResolvedConfig<UserConfig, c12.ConfigLayerMeta>>;
declare const defineConfig: c12.DefineConfig<UserConfig, c12.ConfigLayerMeta>;
declare function initConfig(cwd: string): Promise<void>;

declare const CONFIG_NAME = "tailwindcss-mangle";

declare function getDefaultPatchConfig(): PatchUserConfig;
declare function getDefaultMangleUserConfig(): MangleUserConfig;
declare function getDefaultUserConfig(): UserConfig;

export { CONFIG_NAME, type ClassMapOutputItem, type ClassMapOutputOptions, type MangleUserConfig, type OutputUserConfig, type PatchUserConfig, type TailwindcssUserConfig, type TailwindcssV2PatchConfig, type TailwindcssV3PatchConfig, type TailwindcssV4PatchConfig, type UserConfig, defineConfig, getConfig, getDefaultMangleUserConfig, getDefaultPatchConfig, getDefaultUserConfig, initConfig };
