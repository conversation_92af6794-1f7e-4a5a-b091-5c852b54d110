import * as defu from 'defu';
export { defu } from 'defu';

interface IClassGeneratorContextItem {
    name: string;
    usedBy: Set<string>;
}
interface IClassGeneratorOptions {
    reserveClassName?: (string | RegExp)[];
    customGenerate?: (original: string, opts: IClassGeneratorOptions, context: Record<string, any>) => string | undefined;
    log?: boolean;
    exclude?: (string | RegExp)[];
    include?: (string | RegExp)[];
    ignoreClass?: (string | RegExp)[];
    classPrefix?: string;
}
interface IClassGenerator {
    newClassMap: Record<string, IClassGeneratorContextItem>;
    newClassSize: number;
    context: Record<string, any>;
}

declare class ClassGenerator implements IClassGenerator {
    newClassMap: Record<string, IClassGeneratorContextItem>;
    newClassSize: number;
    context: Record<string, any>;
    opts: IClassGeneratorOptions;
    classPrefix: string;
    constructor(opts?: IClassGeneratorOptions);
    defaultClassGenerate(): string;
    ignoreClassName(className: string): boolean;
    includeFilePath(filePath: string): boolean;
    excludeFilePath(filePath: string): boolean;
    isFileIncluded(filePath: string): boolean;
    transformCssClass(className: string): string;
    generateClassName(original: string): IClassGeneratorContextItem;
}

declare function escapeStringRegexp(str: string): string;
interface MakeRegexOptions {
    /**
     * 这是为了进行精确提取用的
     * 比如同时出现了 bg-500 bg-500/50,
     * true 只会提取 bg-500
     * 而 false 会提取 2 个 bg-500
     */
    exact?: boolean;
}
declare function makeRegex(str: string, options?: MakeRegexOptions): RegExp;

declare const validateFilterRE: RegExp;
declare function isValidSelector(selector?: string): selector is string;
declare function splitCode(code: string, options?: {
    splitQuote?: boolean;
}): string[];

declare const defuOverrideArray: <Source extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
}, Defaults extends Array<{
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} | (number | boolean | any[] | Record<never, any> | null | undefined)>>(source: Source, ...defaults: Defaults) => defu.Defu<Source, Defaults>;
declare const preserveClassNames: string[];
declare function defaultMangleClassFilter(className: string): boolean;
declare function groupBy<T>(arr: T[], cb: (arg: T) => string): Record<string, T[]>;
declare const acceptChars: string[];
declare function stripEscapeSequence(words: string): string;
declare function isRegexp(value: unknown): boolean;
declare function isMap(value: unknown): boolean;
declare function regExpTest(arr: (string | RegExp)[] | undefined, str: string): boolean;

export { ClassGenerator, type IClassGenerator, type IClassGeneratorContextItem, type IClassGeneratorOptions, type MakeRegexOptions, acceptChars, defaultMangleClassFilter, defuOverrideArray, escapeStringRegexp, groupBy, isMap, isRegexp, isValidSelector, makeRegex, preserveClassNames, regExpTest, splitCode, stripEscapeSequence, validateFilterRE };
