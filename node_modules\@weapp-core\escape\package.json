{"name": "@weapp-core/escape", "version": "4.0.1", "description": "escape class name in weapp", "author": "ice breaker <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/sonofmagic/weapp-core/tree/main/packages/escape#readme", "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/weapp-core.git", "directory": "packages/escape"}, "bugs": {"url": "https://github.com/sonofmagic/weapp-core/issues"}, "keywords": ["weapp", "escape", "css"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "dependencies": {}, "scripts": {"build": "unbuild", "test": "vitest run --coverage.enabled", "test:dev": "vitest --coverage.enabled", "release": "pnpm publish"}}