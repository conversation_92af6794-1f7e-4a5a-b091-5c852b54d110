<template>
  <view class="h-screen bg-gray-50 flex flex-col relative">
    <!-- 自定义导航栏 -->
    <view class="relative w-full mb-4">
      <!-- 导航栏主体 -->
      <view
        class="w-full bg-gradient-to-r from-primary-500 to-primary-600 relative overflow-hidden"
        :style="navBarStyle"
      >
        <!-- 装饰背景 -->
        <view class="absolute inset-0 opacity-10">
          <view
            class="absolute top-4 right-8 w-12 h-12 rounded-full bg-white"
          ></view>
          <view
            class="absolute bottom-6 left-16 w-6 h-6 rounded-full bg-white"
          ></view>
          <view
            class="absolute top-8 left-1/3 w-3 h-3 rounded-full bg-white"
          ></view>
        </view>

        <!-- 返回按钮 -->
        <view
          :style="navBarButtonStyle"
          @click="goBack"
          class="absolute left-0 top-0 flex items-center h-full pl-4 z-10"
        >
          <view
            class="w-9 h-9 rounded-full bg-white bg-opacity-20 flex items-center justify-center backdrop-blur-sm"
          >
            <i class="fas fa-arrow-left text-white text-base"></i>
          </view>
        </view>

        <!-- 导航内容 -->
        <view class="px-4 py-6 relative z-10">
          <!-- 标题区域 -->
          <view class="text-center mb-4">
            <view class="flex items-center justify-center mb-2">
              <view
                class="w-10 h-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3 backdrop-blur-sm"
              >
                <i class="fas fa-graduation-cap text-white text-lg"></i>
              </view>
              <text class="text-white text-xl font-bold">智能模拟考试</text>
            </view>
            <text class="text-white text-sm opacity-90"
              >AI智能出题 · 个性化配置 · 精准评估</text
            >
          </view>

          <!-- 功能介绍 -->
          <view class="bg-white bg-opacity-15 rounded-xl p-4 backdrop-blur-sm">
            <view class="flex items-center justify-between">
              <view class="flex items-center flex-1">
                <view
                  class="w-8 h-8 rounded-full bg-white bg-opacity-30 flex items-center justify-center mr-3"
                >
                  <i class="fas fa-cogs text-white text-sm"></i>
                </view>
                <view class="flex-1">
                  <text class="text-white text-sm font-medium block"
                    >自定义考试规则</text
                  >
                  <text class="text-white text-xs opacity-80"
                    >题型配置 · 分值设定 · 时长控制</text
                  >
                </view>
              </view>
              <view class="flex items-center ml-4">
                <view
                  class="w-8 h-8 rounded-full bg-white bg-opacity-30 flex items-center justify-center mr-3"
                >
                  <i class="fas fa-chart-line text-white text-sm"></i>
                </view>
                <view>
                  <text class="text-white text-sm font-medium block"
                    >智能分析</text
                  >
                  <text class="text-white text-xs opacity-80"
                    >成绩统计 · 错题分析</text
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主体内容 -->
    <view class="flex-1 px-4 pb-16">
      <!-- 存在未完成考试的情况 -->
      <view
        v-if="hasUnfinishedExam"
        class="bg-white rounded-lg shadow p-4 mb-3"
      >
        <view class="text-center">
          <view class="mb-4">
            <i class="fas fa-clock text-orange-500 text-4xl"></i>
          </view>
          <text class="text-xl font-bold text-gray-800 mb-2 block"
            >未完成考试</text
          >
          <text class="text-gray-500 text-sm mb-6 block"
            >您有一次模拟考试尚未完成，可以选择继续作答或重新开始</text
          >

          <view class="space-y-3">
            <button
              class="w-full bg-primary-500 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center"
              @click="continueExam"
            >
              <i class="fas fa-play mr-2"></i>继续考试
            </button>
            <button
              class="w-full border border-solid border-gray-300 bg-white text-gray-600 py-3 rounded-lg text-sm font-medium flex items-center justify-center"
              @click="resetExam"
            >
              <i class="fas fa-redo mr-2"></i>重新开始
            </button>
          </view>
        </view>
      </view>
      <!-- 不存在未完成考试，显示规则设置 -->
      <view v-else class="bg-white rounded-xl shadow-sm p-4">
        <!-- 题型配置 -->
        <view class="mb-5">
          <!-- 题型配置标题 -->
          <view class="flex items-center mb-3">
            <i class="fas fa-list-alt text-primary-500 mr-2"></i>
            <text class="text-base font-semibold text-gray-800">题型配置</text>
          </view>

          <!-- 题型列表 -->
          <block v-for="(rule, index) in rules" :key="rule.type">
            <view class="mb-3 bg-gray-50 rounded-lg p-3">
              <!-- 题型标题 -->
              <view class="flex items-center justify-between mb-2">
                <text class="text-base font-medium text-gray-800">{{
                  rule.label
                }}</text>
                <text class="text-sm text-gray-500"
                  >题库{{ rule.total }}题</text
                >
              </view>

              <!-- 设置项 -->
              <view class="flex items-center space-x-3">
                <!-- 出题数量 -->
                <view class="flex-1">
                  <text class="text-sm text-gray-600 mb-1 block">出题数量</text>
                  <view
                    class="flex items-center bg-white rounded-lg border border-solid border-gray-200 p-1"
                  >
                    <button
                      @click="decreaseCount(index)"
                      class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-minus text-sm"></i>
                    </button>
                    <input
                      type="number"
                      v-model.number="rule.count"
                      min="0"
                      :max="rule.total"
                      class="flex-1 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
                      readonly
                    />
                    <button
                      @click="increaseCount(index)"
                      class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-plus text-sm"></i>
                    </button>
                  </view>
                </view>

                <!-- 每题分值 -->
                <view class="flex-1">
                  <text class="text-sm text-gray-600 mb-1 block">每题分值</text>
                  <view
                    class="flex items-center bg-white rounded-lg border border-solid border-gray-200 p-1"
                  >
                    <button
                      @click="decreaseScore(index)"
                      class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-minus text-sm"></i>
                    </button>
                    <input
                      type="number"
                      v-model.number="rule.score"
                      min="1"
                      class="flex-1 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
                      readonly
                    />
                    <button
                      @click="increaseScore(index)"
                      class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-plus text-sm"></i>
                    </button>
                  </view>
                </view>
              </view>
            </view>
          </block>
        </view>

        <!-- 及格分数 -->
        <view class="mb-4">
          <view class="flex items-center justify-between">
            <view class="flex items-center">
              <text class="text-base font-semibold text-gray-800"
                >及格分数</text
              >
              <text class="text-sm text-gray-500 ml-2"
                >（总分{{ totalScore }}分）</text
              >
            </view>
            <view
              class="flex items-center bg-gray-50 rounded-lg border border-solid border-gray-200 p-1"
            >
              <button
                @click="decreasePassScore()"
                class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
              >
                <i class="fas fa-minus text-sm"></i>
              </button>
              <input
                type="number"
                v-model.number="passScore"
                :max="totalScore"
                min="1"
                class="w-16 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
                readonly
              />
              <button
                @click="increasePassScore()"
                class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
              >
                <i class="fas fa-plus text-sm"></i>
              </button>
            </view>
          </view>
        </view>

        <!-- 考试时长 -->
        <view class="mb-4">
          <view class="flex items-center justify-between">
            <text class="text-base font-semibold text-gray-800">考试时长</text>
            <view
              class="flex items-center bg-gray-50 rounded-lg border border-solid border-gray-200 p-1"
            >
              <button
                @click="decreaseDuration()"
                class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
              >
                <i class="fas fa-minus text-sm"></i>
              </button>
              <input
                type="number"
                v-model.number="duration"
                min="1"
                step="1"
                class="w-16 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
                readonly
              />
              <text class="text-sm text-gray-500 mr-2">分钟</text>
              <button
                @click="increaseDuration()"
                class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
              >
                <i class="fas fa-plus text-sm"></i>
              </button>
            </view>
          </view>
        </view>

        <!-- 选项乱序 -->
        <view class="mb-4">
          <view class="flex items-center justify-between">
            <text class="text-base font-semibold text-gray-800">选项乱序</text>
            <view class="flex items-center">
              <switch
                :checked="shuffleOption"
                @change="(e) => (shuffleOption = e.detail.value)"
                color="#3b82f6"
                class="mr-3"
              />
              <text class="text-sm text-gray-500">{{
                shuffleOption ? "开启" : "关闭"
              }}</text>
            </view>
          </view>
        </view>

        <!-- 开始考试按钮 -->
        <button
          class="w-full bg-primary-500 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center"
          @click="startExam"
        >
          <i class="fas fa-play mr-2"></i>开始考试
        </button>
      </view>
    </view>

    <!-- 右下角固定历史记录按钮 -->
    <view class="fixed bottom-6 right-6 z-50">
      <button
        class="w-12 h-12 rounded-full bg-primary-500 text-white shadow-lg flex items-center justify-center"
        @click="goHistory"
      >
        <i class="fas fa-history text-xl"></i>
      </button>
      <!-- 小红点提示（可选） -->
      <view
        v-if="hasNewHistory"
        class="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
      >
        <text class="text-white text-xs font-bold">!</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      navBarButtonStyle: {},
      navBarStyle: {}, // 新增导航栏样式
      hasUnfinishedExam: true, // 需根据接口实际判断，默认改为false以展示规则设置
      hasNewHistory: false, // 是否有新的历史记录提示
      rules: [
        // 示例题型，实际应根据题库返回的题型和数量生成
        { type: 1, label: "单选题", total: 20, count: 10, score: 2 },
        { type: 2, label: "多选题", total: 10, count: 5, score: 3 },
        { type: 3, label: "判断题", total: 5, count: 5, score: 1 },
      ],
      passScore: 60,
      duration: 60,
      shuffleOption: true,
    };
  },
  computed: {
    totalScore() {
      return this.rules.reduce((sum, r) => sum + r.count * r.score, 0);
    },
  },
  methods: {
    setNavBarButtonStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const menuButton = uni.getMenuButtonBoundingClientRect();
      style = {
        position: "absolute",
        left: menuButton.left + "px",
        top: menuButton.top + "px",
        width: menuButton.width + "px",
        height: menuButton.height + "px",
        "z-index": 20,
      };
      // #endif
      // #ifdef H5
      style = {
        position: "absolute",
        left: "16px",
        top: "16px",
        width: "40px",
        height: "40px",
        "z-index": 20,
      };
      // #endif
      this.navBarButtonStyle = style;
    },
    setNavBarStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const sys = uni.getSystemInfoSync();
      const menuButton = uni.getMenuButtonBoundingClientRect();
      const navTop = sys.statusBarHeight;
      style = {
        "padding-top": navTop + "px",
      };
      // #endif
      // #ifdef H5
      style = {
        "padding-top": "16px",
      };
      // #endif
      this.navBarStyle = style;
    },
    goBack() {
      uni.navigateBack();
    },
    // 检查是否有未完成的考试
    checkUnfinishedExam() {
      // TODO: 调用接口判断
      // this.hasUnfinishedExam = ...
    },
    continueExam() {
      // 跳转到考试页面，带上考试ID
      uni.navigateTo({ url: "/pages/practice/do-chapter?mode=mock" });
    },
    resetExam() {
      // 清除未完成考试，重置表单
      this.hasUnfinishedExam = false;
      // TODO: 调用接口清除未完成考试
    },
    startExam() {
      if (this.passScore > this.totalScore) {
        uni.showToast({ title: "及格分不能超过总分", icon: "none" });
        return;
      }
      if (this.duration < 1 || !Number.isInteger(this.duration)) {
        uni.showToast({ title: "考试时长需为正整数", icon: "none" });
        return;
      }
      // TODO: 发起考试接口，带上规则参数
      // 成功后跳转到考试页面
      uni.navigateTo({ url: "/pages/practice/do-chapter?mode=mock" });
    },
    goHistory() {
      uni.navigateTo({ url: "/pages/practice/mock-exam-history" });
    },

    // +/- 按钮方法
    decreaseCount(index) {
      if (this.rules[index].count > 0) {
        this.rules[index].count--;
      }
    },
    increaseCount(index) {
      if (this.rules[index].count < this.rules[index].total) {
        this.rules[index].count++;
      }
    },
    decreaseScore(index) {
      if (this.rules[index].score > 1) {
        this.rules[index].score--;
      }
    },
    increaseScore(index) {
      this.rules[index].score++;
    },
    decreasePassScore() {
      if (this.passScore > 1) {
        this.passScore--;
      }
    },
    increasePassScore() {
      if (this.passScore < this.totalScore) {
        this.passScore++;
      }
    },
    decreaseDuration() {
      if (this.duration > 1) {
        this.duration--;
      }
    },
    increaseDuration() {
      this.duration++;
    },
  },
  onLoad(options) {
    this.setNavBarButtonStyle();
    this.setNavBarStyle();
    // TODO: 根据bankId拉取题型和数量，检查未完成考试
    // this.checkUnfinishedExam();
  },
};
</script>
