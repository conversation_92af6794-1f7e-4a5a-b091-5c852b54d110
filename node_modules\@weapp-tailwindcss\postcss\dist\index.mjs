import "./chunk-WAXGOBY2.mjs";

// src/handler.ts
import { defuOverrideArray } from "@weapp-tailwindcss/shared";
import postcss from "postcss";

// src/defaults.ts
function getDefaultOptions() {
  return {
    // https://github.com/postcss/postcss-calc
    cssPresetEnv: {
      features: {
        "cascade-layers": true,
        "is-pseudo-class": {
          specificityMatchingName: "weapp-tw-ig"
        },
        "oklab-function": true,
        "color-mix": true,
        "custom-properties": false
      },
      autoprefixer: {
        add: false
      }
    },
    cssRemoveProperty: true,
    // cssRemoveAtSupports: true,
    // cssRemoveAtMedia: true,
    cssSelectorReplacement: {
      root: "page",
      universal: ["view", "text"]
    }
  };
}

// src/plugins/index.ts
import postcssPresetEnv from "postcss-preset-env";
import postcssRem2rpx from "postcss-rem-to-responsive-pixel";

// src/plugins/ctx.ts
function createContext() {
  const variablesScopeWeakMap = /* @__PURE__ */ new WeakMap();
  function isVariablesScope(rule) {
    return variablesScopeWeakMap.get(rule) === true;
  }
  function markVariablesScope(rule) {
    variablesScopeWeakMap.set(rule, true);
  }
  return {
    variablesScopeWeakMap,
    isVariablesScope,
    markVariablesScope
  };
}

// src/plugins/post.ts
import { defu } from "@weapp-tailwindcss/shared";

// src/constants.ts
var postcssPlugin = "postcss-weapp-tailwindcss-rename-plugin";

// src/selectorParser.ts
import psp from "postcss-selector-parser";

// src/shared.ts
import { escape, MappingChars2String } from "@weapp-core/escape";
function internalCssSelectorReplacer(selectors, options = {
  escapeMap: MappingChars2String
}) {
  const { mangleContext, escapeMap } = options;
  if (mangleContext) {
    selectors = mangleContext.cssHandler(selectors);
  }
  return escape(selectors, {
    map: escapeMap
  });
}
function composeIsPseudo(strs) {
  if (typeof strs === "string") {
    return strs;
  }
  if (strs.length > 1) {
    return `:is(${strs.join(",")})`;
  }
  return strs.join("");
}

// src/selectorParser.ts
function mklist(node) {
  return [
    node,
    psp.combinator({
      value: "+"
    }),
    node.clone()
  ];
}
function composeIsPseudoAst(strs) {
  if (typeof strs === "string") {
    return mklist(psp.tag({
      value: strs
    }));
  }
  if (strs.length > 1) {
    return mklist(psp.pseudo({
      value: ":is",
      nodes: strs.map(
        (str) => psp.tag({
          value: str
        })
      )
    }));
  }
  return mklist(psp.tag({
    value: strs[0]
  }));
}
function getCombinatorSelectorAst(options) {
  let childCombinatorReplaceValue = mklist(psp.tag({ value: "view" }));
  const { cssChildCombinatorReplaceValue } = options;
  if (typeof cssChildCombinatorReplaceValue === "string" || Array.isArray(cssChildCombinatorReplaceValue) && cssChildCombinatorReplaceValue.length > 0) {
    childCombinatorReplaceValue = composeIsPseudoAst(cssChildCombinatorReplaceValue);
  }
  return childCombinatorReplaceValue;
}
function createRuleTransform(rule, options) {
  const { escapeMap, mangleContext, cssSelectorReplacement, cssRemoveHoverPseudoClass, uniAppX } = options;
  const transform = (selectors) => {
    selectors.walk((selector, index) => {
      if (!uniAppX) {
        if (selector.type === "class") {
          selector.value = internalCssSelectorReplacer(selector.value, {
            escapeMap,
            mangleContext
          });
        } else if (selector.type === "universal") {
          if (cssSelectorReplacement?.universal) {
            selector.value = composeIsPseudo(cssSelectorReplacement.universal);
          }
        } else if (selector.type === "selector") {
          if (cssRemoveHoverPseudoClass) {
            const node = selector.nodes.find((x) => x.type === "pseudo" && x.value === ":hover");
            if (node) {
              selector.remove();
            }
          }
        } else if (selector.type === "pseudo") {
          if (selector.value === ":root" && cssSelectorReplacement?.root) {
            selector.value = composeIsPseudo(cssSelectorReplacement.root);
          } else if (selector.value === ":where") {
            if (index === 0 && selector.length === 1) {
              selector.walk((node, idx) => {
                if (idx === 0 && node.type === "class") {
                  const nodes2 = node.parent?.nodes;
                  if (nodes2) {
                    const first = nodes2[idx + 1];
                    if (first && first.type === "combinator" && first.value === ">") {
                      const second = nodes2[idx + 2];
                      if (second && second.type === "pseudo" && second.value === ":not" && second.first.first.type === "pseudo" && second.first.first.value === ":last-child") {
                        const ast = getCombinatorSelectorAst(options);
                        second.replaceWith(
                          ...ast
                        );
                      }
                    }
                  }
                }
              });
              selector.replaceWith(...selector.nodes);
              for (const node of rule.nodes) {
                if (node.type === "decl") {
                  if (node.prop === "margin-block-start") {
                    node.prop = "margin-block-end";
                  } else if (node.prop === "margin-block-end") {
                    node.prop = "margin-block-start";
                  } else if (node.prop === "margin-inline-start") {
                    node.prop = "margin-inline-end";
                  } else if (node.prop === "margin-inline-end") {
                    node.prop = "margin-inline-start";
                  } else if (node.prop === "margin-top") {
                    node.prop = "margin-bottom";
                  } else if (node.prop === "margin-bottom") {
                    node.prop = "margin-top";
                  } else if (node.prop === "margin-left") {
                    node.prop = "margin-right";
                  } else if (node.prop === "margin-right") {
                    node.prop = "margin-left";
                  } else if (node.prop === "-webkit-margin-start" || node.prop === "-webkit-margin-end" || node.prop === "-webkit-margin-before" || node.prop === "-webkit-margin-after") {
                    node.remove();
                  }
                }
              }
            }
          }
        } else if (selector.type === "combinator") {
          if (selector.value === ">") {
            const nodes2 = selector.parent?.nodes;
            if (nodes2) {
              const first = nodes2[index + 1];
              if (first && first.type === "pseudo" && first.value === ":not" && (first.first.first.type === "attribute" && first.first.first.attribute === "hidden" || first.first.first.type === "tag" && first.first.first.value === "template")) {
                const second = nodes2[index + 2];
                if (second && second.type === "combinator" && (second.value === "~" || second.value === "+")) {
                  const third = nodes2[index + 3];
                  if (third && third.type === "pseudo" && third.value === ":not" && (third.first.first.type === "attribute" && third.first.first.attribute === "hidden" || third.first.first.type === "tag" && third.first.first.value === "template")) {
                    const ast = getCombinatorSelectorAst(options);
                    selector.parent?.nodes.splice(
                      index + 1,
                      3,
                      ...ast
                    );
                  }
                }
              }
            }
          }
        }
      } else {
        if (selector.type === "class") {
          selector.value = internalCssSelectorReplacer(selector.value, {
            escapeMap,
            mangleContext
          });
        } else {
          selector.remove();
        }
      }
    });
    if (selectors.length === 0) {
      rule.remove();
    }
  };
  return transform;
}
function ruleTransformSync(rule, options) {
  const transformer = psp(createRuleTransform(rule, options));
  return transformer.transformSync(rule, {
    lossless: false,
    updateSelector: true
  });
}
function isOnlyBeforeAndAfterPseudoElement(node) {
  let b = false;
  let a = false;
  psp((selectors) => {
    selectors.walkPseudos((s) => {
      if (s.parent?.length === 1) {
        if (/^:?:before$/.test(s.value)) {
          b = true;
        }
        if (/^:?:after$/.test(s.value)) {
          a = true;
        }
      }
    });
  }).astSync(node);
  return b && a;
}
function getFallbackRemove(rule) {
  const fallbackRemove = psp((selectors) => {
    let maybeImportantId = false;
    selectors.walk((selector, idx) => {
      if (idx === 0 && (selector.type === "id" || selector.type === "class" || selector.type === "attribute")) {
        maybeImportantId = true;
      }
      if (selector.type === "universal") {
        selector.parent?.remove();
      }
      if (selector.type === "pseudo") {
        if (selector.value === ":is") {
          if (maybeImportantId && selector.nodes[0]?.type === "selector") {
            selector.replaceWith(selector.nodes[0]);
          } else {
            selector.parent?.remove();
          }
        } else if (selector.value === ":not") {
          for (const x of selector.nodes) {
            if (x.nodes.length === 1 && x.nodes[0].type === "id" && x.nodes[0].value === "#") {
              x.nodes = [
                psp.tag({
                  value: "#n"
                })
              ];
            }
          }
        } else if (selector.value === ":where") {
          for (const n2 of selector.nodes) {
            for (const node of n2.nodes) {
              if (node.type === "attribute") {
                node.remove();
              }
            }
          }
        }
      }
      if (selector.type === "attribute") {
        if (selector.attribute === "hidden") {
          rule?.remove();
        }
      }
    });
    selectors.walk((selector) => {
      if (selector.type === "pseudo") {
        if (selector.value === ":where") {
          const res = selector.nodes.every((x) => x.nodes.length === 0);
          if (res) {
            selector.remove();
          }
        }
      }
    });
  });
  return fallbackRemove;
}

// src/plugins/post.ts
var OklabSuffix = "in oklab";
var postcssWeappTailwindcssPostPlugin = (options) => {
  const opts = defu(options, {
    isMainChunk: true
  });
  const p = {
    postcssPlugin
  };
  if (opts.isMainChunk) {
    p.OnceExit = (root) => {
      root.walkRules((rule) => {
        getFallbackRemove(rule).transformSync(rule, {
          updateSelector: true,
          lossless: false
        });
        if (rule.selectors.length === 0 || rule.selectors.length === 1 && rule.selector.trim() === "") {
          rule.remove();
        }
        rule.walkDecls((decl) => {
          if (opts.uniAppX && decl.prop.startsWith("--")) {
            decl.remove();
          } else if (decl.prop === "--tw-gradient-position" && decl.value.endsWith(OklabSuffix)) {
            decl.value = decl.value.slice(0, decl.value.length - OklabSuffix.length);
          } else if (/calc\(\s*infinity\s*\*\s*1px/.test(decl.value)) {
            decl.value = "9999px";
          }
        });
        if (opts.uniAppX) {
          if (rule.nodes.length === 0) {
            rule.remove();
          }
        }
      });
      root.walkAtRules((atRule) => {
        if (opts.cssRemoveProperty && atRule.name === "property") {
          atRule.remove();
        }
        atRule.nodes?.length === 0 && atRule.remove();
      });
    };
  }
  if (typeof opts.customRuleCallback === "function") {
    p.Rule = (rule) => {
      opts.customRuleCallback?.(rule, opts);
    };
  }
  return p;
};
postcssWeappTailwindcssPostPlugin.postcss = true;

// src/plugins/pre.ts
import { defu as defu2 } from "@weapp-tailwindcss/shared";

// src/mp.ts
import { Declaration, Rule } from "postcss";

// src/cssVarsV3.ts
var cssVarsV3_default = [
  {
    prop: "--tw-border-spacing-x",
    value: "0"
  },
  {
    prop: "--tw-border-spacing-y",
    value: "0"
  },
  {
    prop: "--tw-translate-x",
    value: "0"
  },
  {
    prop: "--tw-translate-y",
    value: "0"
  },
  {
    prop: "--tw-rotate",
    value: "0"
  },
  {
    prop: "--tw-skew-x",
    value: "0"
  },
  {
    prop: "--tw-skew-y",
    value: "0"
  },
  {
    prop: "--tw-scale-x",
    value: "1"
  },
  {
    prop: "--tw-scale-y",
    value: "1"
  },
  {
    prop: "--tw-pan-x",
    value: " "
  },
  {
    prop: "--tw-pan-y",
    value: " "
  },
  {
    prop: "--tw-pinch-zoom",
    value: " "
  },
  {
    prop: "--tw-scroll-snap-strictness",
    value: "proximity"
  },
  {
    prop: "--tw-gradient-from-position",
    value: " "
  },
  {
    prop: "--tw-gradient-via-position",
    value: " "
  },
  {
    prop: "--tw-gradient-to-position",
    value: " "
  },
  {
    prop: "--tw-ordinal",
    value: " "
  },
  {
    prop: "--tw-slashed-zero",
    value: " "
  },
  {
    prop: "--tw-numeric-figure",
    value: " "
  },
  {
    prop: "--tw-numeric-spacing",
    value: " "
  },
  {
    prop: "--tw-numeric-fraction",
    value: " "
  },
  {
    prop: "--tw-ring-inset",
    value: " "
  },
  {
    prop: "--tw-ring-offset-width",
    value: "0px"
  },
  {
    prop: "--tw-ring-offset-color",
    value: "#fff"
  },
  {
    prop: "--tw-ring-color",
    value: "rgb(59 130 246 / 0.5)"
  },
  {
    prop: "--tw-ring-offset-shadow",
    value: "0 0 #0000"
  },
  {
    prop: "--tw-ring-shadow",
    value: "0 0 #0000"
  },
  {
    prop: "--tw-shadow",
    value: "0 0 #0000"
  },
  {
    prop: "--tw-shadow-colored",
    value: "0 0 #0000"
  },
  {
    prop: "--tw-blur",
    value: " "
  },
  {
    prop: "--tw-brightness",
    value: " "
  },
  {
    prop: "--tw-contrast",
    value: " "
  },
  {
    prop: "--tw-grayscale",
    value: " "
  },
  {
    prop: "--tw-hue-rotate",
    value: " "
  },
  {
    prop: "--tw-invert",
    value: " "
  },
  {
    prop: "--tw-saturate",
    value: " "
  },
  {
    prop: "--tw-sepia",
    value: " "
  },
  {
    prop: "--tw-drop-shadow",
    value: " "
  },
  {
    prop: "--tw-backdrop-blur",
    value: " "
  },
  {
    prop: "--tw-backdrop-brightness",
    value: " "
  },
  {
    prop: "--tw-backdrop-contrast",
    value: " "
  },
  {
    prop: "--tw-backdrop-grayscale",
    value: " "
  },
  {
    prop: "--tw-backdrop-hue-rotate",
    value: " "
  },
  {
    prop: "--tw-backdrop-invert",
    value: " "
  },
  {
    prop: "--tw-backdrop-opacity",
    value: " "
  },
  {
    prop: "--tw-backdrop-saturate",
    value: " "
  },
  {
    prop: "--tw-backdrop-sepia",
    value: " "
  },
  {
    prop: "--tw-contain-size",
    value: " "
  },
  {
    prop: "--tw-contain-layout",
    value: " "
  },
  {
    prop: "--tw-contain-paint",
    value: " "
  },
  {
    prop: "--tw-contain-style",
    value: " "
  }
];

// src/cssVarsV4.ts
function property(ident, initialValue, _syntax) {
  return {
    prop: ident,
    value: initialValue || ""
  };
}
var nullShadow = "0 0 #0000";
var nodes = [
  // https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/src/utilities.ts#L1137
  property("--tw-border-spacing-x", "0", "<length>"),
  property("--tw-border-spacing-y", "0", "<length>"),
  // https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/src/utilities.ts#L1205
  property("--tw-translate-x", "0"),
  property("--tw-translate-y", "0"),
  property("--tw-translate-z", "0"),
  // https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/src/utilities.ts#L1285
  property("--tw-scale-x", "1"),
  property("--tw-scale-y", "1"),
  property("--tw-scale-z", "1"),
  // https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/src/utilities.ts#L1424
  property("--tw-rotate-x"),
  property("--tw-rotate-y"),
  property("--tw-rotate-z"),
  property("--tw-skew-x"),
  property("--tw-skew-y"),
  // https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/src/utilities.ts#L1641C13-L1641C88
  property("--tw-pan-x"),
  property("--tw-pan-y"),
  property("--tw-pinch-zoom"),
  // https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/src/utilities.ts#L1688C38-L1688C95
  property("--tw-scroll-snap-strictness", "proximity", "*"),
  // https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/src/utilities.ts#L1688C38-L1688C95
  property("--tw-space-x-reverse", "0"),
  property("--tw-space-y-reverse", "0"),
  // https://github.com/tailwindlabs/tailwindcss/blob/main/packages/tailwindcss/src/utilities.ts#L2169C22-L2169C60
  property("--tw-border-style", "solid"),
  property("--tw-divide-x-reverse", "0"),
  property("--tw-divide-y-reverse", "0"),
  property("--tw-gradient-position", "initial"),
  property("--tw-gradient-from", "#0000", "<color>"),
  property("--tw-gradient-via", "#0000", "<color>"),
  property("--tw-gradient-to", "#0000", "<color>"),
  property("--tw-gradient-stops", "initial"),
  property("--tw-gradient-via-stops", "initial"),
  property("--tw-gradient-from-position", "0%", "<length-percentage>"),
  property("--tw-gradient-via-position", "50%", "<length-percentage>"),
  property("--tw-gradient-to-position", "100%", "<length-percentage>"),
  property("--tw-mask-linear", "linear-gradient(#fff, #fff)"),
  property("--tw-mask-radial", "linear-gradient(#fff, #fff)"),
  property("--tw-mask-conic", "linear-gradient(#fff, #fff)"),
  property("--tw-mask-left", "linear-gradient(#fff, #fff)"),
  property("--tw-mask-right", "linear-gradient(#fff, #fff)"),
  property("--tw-mask-bottom", "linear-gradient(#fff, #fff)"),
  property("--tw-mask-top", "linear-gradient(#fff, #fff)"),
  property("--tw-mask-linear-position", "0deg"),
  property("--tw-mask-linear-from-position", "0%"),
  property("--tw-mask-linear-to-position", "100%"),
  property("--tw-mask-linear-from-color", "black"),
  property("--tw-mask-linear-to-color", "transparent"),
  property("--tw-mask-radial-from-position", "0%"),
  property("--tw-mask-radial-to-position", "100%"),
  property("--tw-mask-radial-from-color", "black"),
  property("--tw-mask-radial-to-color", "transparent"),
  property("--tw-mask-radial-shape", "ellipse"),
  property("--tw-mask-radial-size", "farthest-corner"),
  property("--tw-mask-radial-position", "center"),
  property("--tw-mask-conic-position", "0deg"),
  property("--tw-mask-conic-from-position", "0%"),
  property("--tw-mask-conic-to-position", "100%"),
  property("--tw-mask-conic-from-color", "black"),
  property("--tw-mask-conic-to-color", "transparent"),
  property("--tw-font-weight"),
  property("--tw-blur"),
  property("--tw-brightness"),
  property("--tw-contrast"),
  property("--tw-grayscale"),
  property("--tw-hue-rotate"),
  property("--tw-invert"),
  property("--tw-opacity"),
  property("--tw-saturate"),
  property("--tw-sepia"),
  property("--tw-drop-shadow"),
  property("--tw-drop-shadow-color"),
  property("--tw-drop-shadow-alpha", "100%", "<percentage>"),
  property("--tw-drop-shadow-size"),
  property("--tw-backdrop-blur"),
  property("--tw-backdrop-brightness"),
  property("--tw-backdrop-contrast"),
  property("--tw-backdrop-grayscale"),
  property("--tw-backdrop-hue-rotate"),
  property("--tw-backdrop-invert"),
  property("--tw-backdrop-opacity"),
  property("--tw-backdrop-saturate"),
  property("--tw-backdrop-sepia"),
  property("--tw-duration", "initial"),
  property("--tw-ease", "initial"),
  property("--tw-content", '""'),
  property("--tw-contain-size"),
  property("--tw-contain-layout"),
  property("--tw-contain-paint"),
  property("--tw-contain-style"),
  property("--tw-leading"),
  property("--tw-tracking"),
  property("--tw-ordinal"),
  property("--tw-slashed-zero"),
  property("--tw-numeric-figure"),
  property("--tw-numeric-spacing"),
  property("--tw-numeric-fraction"),
  property("--tw-outline-style", "solid"),
  property("--tw-text-shadow-color", "initial"),
  property("--tw-text-shadow-alpha", "100%", "<percentage>"),
  property("--tw-shadow", nullShadow),
  property("--tw-shadow-color", "initial"),
  property("--tw-shadow-alpha", "100%", "<percentage>"),
  property("--tw-inset-shadow", nullShadow),
  property("--tw-inset-shadow-color", "initial"),
  property("--tw-inset-shadow-alpha", "100%", "<percentage>"),
  property("--tw-ring-color"),
  property("--tw-ring-shadow", nullShadow),
  property("--tw-inset-ring-color"),
  property("--tw-inset-ring-shadow", nullShadow),
  // Legacy
  property("--tw-ring-inset"),
  property("--tw-ring-offset-width", "0px", "<length>"),
  property("--tw-ring-offset-color", "#fff"),
  property("--tw-ring-offset-shadow", nullShadow)
];
for (const edge of ["top", "right", "bottom", "left"]) {
  nodes.push(
    property(`--tw-mask-${edge}-from-position`, "0%"),
    property(`--tw-mask-${edge}-to-position`, "100%"),
    property(`--tw-mask-${edge}-from-color`, "black"),
    property(`--tw-mask-${edge}-to-color`, "transparent")
  );
}
var cssVarsV4_default = nodes;

// src/mp.ts
var cssVarsV3Nodes = cssVarsV3_default.map((x) => {
  return new Declaration({
    prop: x.prop,
    value: x.value
  });
});
var cssVarsV4Nodes = cssVarsV4_default.map((x) => {
  return new Declaration({
    prop: x.prop,
    value: x.value
  });
});
function testIfVariablesScope(node, count = 2) {
  if (isOnlyBeforeAndAfterPseudoElement(node)) {
    const nodes2 = node.nodes;
    let c = 0;
    for (const tryTestDecl of nodes2) {
      if (tryTestDecl && tryTestDecl.type === "decl" && tryTestDecl.prop.startsWith("--tw-")) {
        c++;
      }
      if (c >= count) {
        return true;
      }
    }
    return false;
  }
  return false;
}
function testIfTwBackdrop(node, count = 2) {
  if (node.type === "rule" && node.selector === "::backdrop") {
    const nodes2 = node.nodes;
    let c = 0;
    for (const tryTestDecl of nodes2) {
      if (tryTestDecl && tryTestDecl.type === "decl" && tryTestDecl.prop.startsWith("--tw-")) {
        c++;
      }
      if (c >= count) {
        return true;
      }
    }
    return false;
  }
  return false;
}
function testIfRootHostForV4(node) {
  return node.type === "rule" && node.selector.includes(":root") && node.selector.includes(":host");
}
function makePseudoVarRule() {
  const pseudoVarRule = new Rule({
    // selectors: ['::before', '::after'],
    selector: "::before,::after"
  });
  pseudoVarRule.append(
    new Declaration({
      prop: "--tw-content",
      value: '""'
    })
  );
  return pseudoVarRule;
}
function remakeCssVarSelector(selectors, options) {
  const { cssPreflightRange, cssSelectorReplacement } = options;
  if (cssPreflightRange === "all" && !selectors.includes(":not(not)")) {
    selectors.push(":not(not)");
  }
  if (cssSelectorReplacement) {
    if (Array.isArray(cssSelectorReplacement.universal)) {
      if (!cssSelectorReplacement.universal.every((x) => {
        return selectors.includes(x);
      }) && !selectors.includes("*")) {
        selectors.unshift("*");
      }
    } else if (typeof cssSelectorReplacement.universal === "string" && !selectors.includes(cssSelectorReplacement.universal) && !selectors.includes("*")) {
      selectors.unshift("*");
    }
  }
  return selectors;
}
function commonChunkPreflight(node, options) {
  const { ctx, cssInjectPreflight, injectAdditionalCssVarScope } = options;
  if (testIfVariablesScope(node)) {
    ctx?.markVariablesScope(node);
    node.selectors = remakeCssVarSelector(node.selectors, options);
    node.before(makePseudoVarRule());
    if (typeof cssInjectPreflight === "function") {
      node.append(...cssInjectPreflight());
    }
  }
  const isTailwindcss4 = options.majorVersion === 4;
  if (injectAdditionalCssVarScope && (isTailwindcss4 ? testIfRootHostForV4(node) : testIfTwBackdrop(node))) {
    const syntheticRule = new Rule({
      selectors: ["*", "::after", "::before"],
      nodes: isTailwindcss4 ? cssVarsV4Nodes : cssVarsV3Nodes
    });
    syntheticRule.selectors = remakeCssVarSelector(syntheticRule.selectors, options);
    node.before(syntheticRule);
    node.before(makePseudoVarRule());
    if (typeof cssInjectPreflight === "function") {
      syntheticRule.append(...cssInjectPreflight());
    }
  }
}

// src/plugins/pre.ts
function isAtMediaHover(atRule) {
  return /media\(\s*hover\s*:\s*hover\s*\)/.test(atRule.name) || atRule.name === "media" && /\(\s*hover\s*:\s*hover\s*\)/.test(atRule.params);
}
function isTailwindcssV4ModernCheck(atRule) {
  return atRule.name === "supports" && [
    /-webkit-hyphens\s*:\s*none/,
    /margin-trim\s*:\s*inline/,
    /-moz-orient\s*:\s*inline/,
    /color\s*:\s*rgb\(\s*from\s+red\s+r\s+g\s+b\s*\)/
  ].every((regex) => regex.test(atRule.params));
}
var postcssWeappTailwindcssPrePlugin = (options) => {
  const opts = defu2(options, { isMainChunk: true });
  const p = {
    postcssPlugin,
    Rule(rule) {
      ruleTransformSync(rule, opts);
    },
    AtRule(atRule) {
      if (isAtMediaHover(atRule)) {
        if (atRule.nodes) {
          atRule.replaceWith(atRule.nodes);
        } else {
          atRule.remove();
        }
      } else if (atRule.name === "supports") {
        if (/color-mix/.test(atRule.params)) {
          atRule.remove();
        }
      } else if (atRule.name === "layer") {
        if (atRule.nodes === void 0 || Array.isArray(atRule.nodes) && atRule.nodes.length === 0) {
          atRule.remove();
        }
      }
    }
  };
  if (opts.isMainChunk) {
    let layerProperties;
    p.Once = (root) => {
      root.walkAtRules((atRule) => {
        if (atRule.name === "layer") {
          if (atRule.params === "properties") {
            if (atRule.nodes === void 0 || atRule.nodes?.length === 0) {
              layerProperties = atRule;
            } else if (atRule.first?.type === "atrule" && isTailwindcssV4ModernCheck(atRule.first)) {
              if (layerProperties) {
                layerProperties.replaceWith(atRule.first.nodes);
                atRule.remove();
              } else {
                atRule.replaceWith(atRule.first.nodes);
              }
            }
          } else {
            atRule.replaceWith(atRule.nodes);
          }
        } else if (isTailwindcssV4ModernCheck(atRule)) {
          if (atRule.first?.type === "atrule" && atRule.first.name === "layer") {
            atRule.replaceWith(atRule.first.nodes);
          }
        }
      });
      root.walkRules((rule) => {
        commonChunkPreflight(rule, opts);
      });
    };
  }
  return p;
};
postcssWeappTailwindcssPrePlugin.postcss = true;

// ../../node_modules/.pnpm/@csstools+postcss-is-pseudo-class@5.0.3_postcss@8.5.6/node_modules/@csstools/postcss-is-pseudo-class/dist/index.mjs
import e2 from "postcss-selector-parser";

// ../../node_modules/.pnpm/@csstools+selector-specificity@5.0.0_postcss-selector-parser@7.1.0/node_modules/@csstools/selector-specificity/dist/index.mjs
import e from "postcss-selector-parser";
function compare(e3, t) {
  return e3.a === t.a ? e3.b === t.b ? e3.c - t.c : e3.b - t.b : e3.a - t.a;
}
function selectorSpecificity(t, s) {
  const i = s?.customSpecificity?.(t);
  if (i) return i;
  if (!t) return { a: 0, b: 0, c: 0 };
  let c = 0, n2 = 0, o2 = 0;
  if ("universal" == t.type) return { a: 0, b: 0, c: 0 };
  if ("id" === t.type) c += 1;
  else if ("tag" === t.type) o2 += 1;
  else if ("class" === t.type) n2 += 1;
  else if ("attribute" === t.type) n2 += 1;
  else if (isPseudoElement(t)) switch (t.value.toLowerCase()) {
    case "::slotted":
      if (o2 += 1, t.nodes && t.nodes.length > 0) {
        const e3 = specificityOfMostSpecificListItem(t.nodes, s);
        c += e3.a, n2 += e3.b, o2 += e3.c;
      }
      break;
    case "::view-transition-group":
    case "::view-transition-image-pair":
    case "::view-transition-old":
    case "::view-transition-new":
      return t.nodes && 1 === t.nodes.length && "selector" === t.nodes[0].type && selectorNodeContainsNothingOrOnlyUniversal(t.nodes[0]) ? { a: 0, b: 0, c: 0 } : { a: 0, b: 0, c: 1 };
    default:
      o2 += 1;
  }
  else if (e.isPseudoClass(t)) switch (t.value.toLowerCase()) {
    case ":-webkit-any":
    case ":any":
    default:
      n2 += 1;
      break;
    case ":-moz-any":
    case ":has":
    case ":is":
    case ":matches":
    case ":not":
      if (t.nodes && t.nodes.length > 0) {
        const e3 = specificityOfMostSpecificListItem(t.nodes, s);
        c += e3.a, n2 += e3.b, o2 += e3.c;
      }
      break;
    case ":where":
      break;
    case ":nth-child":
    case ":nth-last-child":
      if (n2 += 1, t.nodes && t.nodes.length > 0) {
        const i2 = t.nodes[0].nodes.findIndex((e3) => "tag" === e3.type && "of" === e3.value.toLowerCase());
        if (i2 > -1) {
          const a = e.selector({ nodes: [], value: "" });
          t.nodes[0].nodes.slice(i2 + 1).forEach((e3) => {
            a.append(e3.clone());
          });
          const r = [a];
          t.nodes.length > 1 && r.push(...t.nodes.slice(1));
          const l = specificityOfMostSpecificListItem(r, s);
          c += l.a, n2 += l.b, o2 += l.c;
        }
      }
      break;
    case ":local":
    case ":global":
      t.nodes && t.nodes.length > 0 && t.nodes.forEach((e3) => {
        const t2 = selectorSpecificity(e3, s);
        c += t2.a, n2 += t2.b, o2 += t2.c;
      });
      break;
    case ":host":
    case ":host-context":
      if (n2 += 1, t.nodes && t.nodes.length > 0) {
        const e3 = specificityOfMostSpecificListItem(t.nodes, s);
        c += e3.a, n2 += e3.b, o2 += e3.c;
      }
      break;
    case ":active-view-transition":
    case ":active-view-transition-type":
      return { a: 0, b: 1, c: 0 };
  }
  else e.isContainer(t) && t.nodes?.length > 0 && t.nodes.forEach((e3) => {
    const t2 = selectorSpecificity(e3, s);
    c += t2.a, n2 += t2.b, o2 += t2.c;
  });
  return { a: c, b: n2, c: o2 };
}
function specificityOfMostSpecificListItem(e3, t) {
  let s = { a: 0, b: 0, c: 0 };
  return e3.forEach((e4) => {
    const i = selectorSpecificity(e4, t);
    compare(i, s) < 0 || (s = i);
  }), s;
}
function isPseudoElement(t) {
  return e.isPseudoElement(t);
}
function selectorNodeContainsNothingOrOnlyUniversal(e3) {
  if (!e3) return false;
  if (!e3.nodes) return false;
  const t = e3.nodes.filter((e4) => "comment" !== e4.type);
  return 0 === t.length || 1 === t.length && "universal" === t[0].type;
}

// ../../node_modules/.pnpm/@csstools+postcss-is-pseudo-class@5.0.3_postcss@8.5.6/node_modules/@csstools/postcss-is-pseudo-class/dist/index.mjs
function alwaysValidSelector(s) {
  const o2 = e2().astSync(s);
  let n2 = true;
  return o2.walk((e3) => {
    if ("class" !== e3.type && "comment" !== e3.type && "id" !== e3.type && "root" !== e3.type && "selector" !== e3.type && "string" !== e3.type && "tag" !== e3.type && "universal" !== e3.type && ("attribute" !== e3.type || e3.insensitive) && ("combinator" !== e3.type || "+" !== e3.value && ">" !== e3.value && "~" !== e3.value && " " !== e3.value) && ("pseudo" !== e3.type || e3.nodes?.length || ":hover" !== e3.value.toLowerCase() && ":focus" !== e3.value.toLowerCase())) {
      if ("pseudo" === e3.type && 1 === e3.nodes?.length && ":not" === e3.value.toLowerCase()) {
        let s2 = true;
        if (e3.nodes[0].walkCombinators(() => {
          s2 = false;
        }), s2) return;
      }
      return n2 = false, false;
    }
  }), n2;
}
function sortCompoundSelectorsInsideComplexSelector(s) {
  if (!s || !s.nodes || 1 === s.nodes.length) return;
  const o2 = [];
  let n2 = [];
  for (let t2 = 0; t2 < s.nodes.length; t2++) "combinator" !== s.nodes[t2].type ? e2.isPseudoElement(s.nodes[t2]) ? (o2.push(n2), n2 = [s.nodes[t2]]) : n2.push(s.nodes[t2]) : (o2.push(n2), o2.push([s.nodes[t2]]), n2 = []);
  o2.push(n2);
  const t = [];
  for (let e3 = 0; e3 < o2.length; e3++) {
    const s2 = o2[e3];
    s2.sort((e4, s3) => "selector" === e4.type && "selector" === s3.type && e4.nodes.length && s3.nodes.length ? selectorTypeOrder(e4.nodes[0], e4.nodes[0].type) - selectorTypeOrder(s3.nodes[0], s3.nodes[0].type) : "selector" === e4.type && e4.nodes.length ? selectorTypeOrder(e4.nodes[0], e4.nodes[0].type) - selectorTypeOrder(s3, s3.type) : "selector" === s3.type && s3.nodes.length ? selectorTypeOrder(e4, e4.type) - selectorTypeOrder(s3.nodes[0], s3.nodes[0].type) : selectorTypeOrder(e4, e4.type) - selectorTypeOrder(s3, s3.type));
    const n3 = new Set(s2.map((e4) => e4.type)), r = n3.has("universal") && (n3.has("tag") || n3.has("attribute") || n3.has("class") || n3.has("id") || n3.has("pseudo"));
    for (let e4 = 0; e4 < s2.length; e4++) "universal" === s2[e4].type && r ? s2[e4].remove() : t.push(s2[e4]);
  }
  s.removeAll();
  for (let o3 = t.length - 1; o3 >= 0; o3--) {
    const n3 = t[o3 - 1];
    if (t[o3].remove(), n3 && "tag" === n3.type && "tag" === t[o3].type) {
      const n4 = e2.pseudo({ value: ":is", nodes: [e2.selector({ value: "", nodes: [t[o3]] })] });
      n4.parent = s, s.nodes.unshift(n4);
    } else t[o3].parent = s, s.nodes.unshift(t[o3]);
  }
}
function selectorTypeOrder(s, n2) {
  return e2.isPseudoElement(s) ? o.pseudoElement : o[n2];
}
var o = { universal: 0, tag: 1, pseudoElement: 2, id: 3, class: 4, attribute: 5, pseudo: 6, selector: 7, string: 8, root: 9, comment: 10 };
function childAdjacentChild(e3) {
  return !(!e3 || !e3.nodes) && ("selector" === e3.type && (3 === e3.nodes.length && (!(!e3.nodes[0] || "pseudo" !== e3.nodes[0].type || ":-csstools-matches" !== e3.nodes[0].value) && (!(!e3.nodes[1] || "combinator" !== e3.nodes[1].type || "+" !== e3.nodes[1].value && "~" !== e3.nodes[1].value) && (!(!e3.nodes[2] || "pseudo" !== e3.nodes[2].type || ":-csstools-matches" !== e3.nodes[2].value) && (!(!e3.nodes[0].nodes || 1 !== e3.nodes[0].nodes.length) && ("selector" === e3.nodes[0].nodes[0].type && (!(!e3.nodes[0].nodes[0].nodes || 3 !== e3.nodes[0].nodes[0].nodes.length) && (!(!e3.nodes[0].nodes[0].nodes || "combinator" !== e3.nodes[0].nodes[0].nodes[1].type || ">" !== e3.nodes[0].nodes[0].nodes[1].value) && (!(!e3.nodes[2].nodes || 1 !== e3.nodes[2].nodes.length) && ("selector" === e3.nodes[2].nodes[0].type && (!(!e3.nodes[2].nodes[0].nodes || 3 !== e3.nodes[2].nodes[0].nodes.length) && (!(!e3.nodes[2].nodes[0].nodes || "combinator" !== e3.nodes[2].nodes[0].nodes[1].type || ">" !== e3.nodes[2].nodes[0].nodes[1].value) && (e3.nodes[0].nodes[0].insertAfter(e3.nodes[0].nodes[0].nodes[0], e3.nodes[2].nodes[0].nodes[0].clone()), e3.nodes[2].nodes[0].nodes[1].remove(), e3.nodes[2].nodes[0].nodes[0].remove(), e3.nodes[0].replaceWith(e3.nodes[0].nodes[0]), e3.nodes[2].replaceWith(e3.nodes[2].nodes[0]), true))))))))))))));
}
function isInCompoundWithOneOtherElement(s) {
  if (!s || !s.nodes) return false;
  if (!e2.isSelector(s)) return false;
  if (2 !== s.nodes.length) return false;
  let o2 = -1, n2 = -1;
  s.nodes[0] && e2.isPseudoClass(s.nodes[0]) && ":-csstools-matches" === s.nodes[0].value ? (o2 = 0, n2 = 1) : s.nodes[1] && e2.isPseudoClass(s.nodes[1]) && ":-csstools-matches" === s.nodes[1].value && (o2 = 1, n2 = 0);
  const t = s.nodes[o2];
  if (!t || !e2.isPseudoClass(t) || 1 !== t.nodes.length) return false;
  const r = s.nodes[n2];
  return !!r && (!e2.isCombinator(r) && (!e2.isPseudoElement(r) && (t.nodes[0].append(r.clone()), t.replaceWith(...t.nodes[0].nodes), r.remove(), true)));
}
function isPseudoInFirstCompound(s) {
  if (!s || !s.nodes) return false;
  if (!e2.isSelector(s)) return false;
  let o2 = -1;
  for (let n3 = 0; n3 < s.nodes.length; n3++) {
    const t2 = s.nodes[n3];
    if (e2.isCombinator(t2)) return false;
    if (e2.isPseudoElement(t2)) return false;
    if (e2.isPseudoClass(t2)) {
      const e3 = t2;
      if (":-csstools-matches" === e3.value) {
        if (!e3.nodes || 1 !== e3.nodes.length) return false;
        o2 = n3;
        break;
      }
    }
  }
  if (-1 === o2) return false;
  const n2 = s.nodes[o2];
  if (!n2 || !e2.isPseudoClass(n2)) return false;
  const t = s.nodes.slice(0, o2), r = s.nodes.slice(o2 + 1);
  return t.forEach((e3) => {
    n2.nodes[0].append(e3.clone());
  }), r.forEach((e3) => {
    n2.nodes[0].append(e3.clone());
  }), n2.replaceWith(...n2.nodes), t.forEach((e3) => {
    e3.remove();
  }), r.forEach((e3) => {
    e3.remove();
  }), true;
}
function samePrecedingElement(s) {
  if (!s || !s.nodes) return false;
  if ("selector" !== s.type) return false;
  let o2 = -1;
  for (let n3 = 0; n3 < s.nodes.length; n3++) {
    const t2 = s.nodes[n3];
    if (e2.isCombinator(t2)) {
      o2 = n3;
      break;
    }
    if (e2.isPseudoElement(t2)) return false;
  }
  if (-1 === o2) return false;
  const n2 = o2 + 1;
  if (!s.nodes[o2] || "combinator" !== s.nodes[o2].type || ">" !== s.nodes[o2].value && "+" !== s.nodes[o2].value) return false;
  const t = s.nodes[o2].value;
  if (!s.nodes[n2] || "pseudo" !== s.nodes[n2].type || ":-csstools-matches" !== s.nodes[n2].value) return false;
  if (!s.nodes[n2].nodes || 1 !== s.nodes[n2].nodes.length) return false;
  if ("selector" !== s.nodes[n2].nodes[0].type) return false;
  if (!s.nodes[n2].nodes[0].nodes || 3 !== s.nodes[n2].nodes[0].nodes.length) return false;
  if (!s.nodes[n2].nodes[0].nodes || "combinator" !== s.nodes[n2].nodes[0].nodes[1].type || s.nodes[n2].nodes[0].nodes[1].value !== t) return false;
  const r = s.nodes[n2];
  if (!r || !e2.isPseudoClass(r)) return false;
  const d = s.nodes.slice(0, o2), l = s.nodes.slice(n2 + 1);
  return s.each((e3) => {
    e3.remove();
  }), d.forEach((e3) => {
    s.append(e3);
  }), r.nodes[0].nodes.forEach((e3) => {
    s.append(e3);
  }), l.forEach((e3) => {
    s.append(e3);
  }), true;
}
function complexSelectors(s, o2, n2, t) {
  return s.flatMap((s2) => {
    if (-1 === s2.indexOf(":-csstools-matches") && -1 === s2.toLowerCase().indexOf(":is")) return s2;
    const r = e2().astSync(s2);
    return r.walkPseudos((s3) => {
      if (":is" === s3.value.toLowerCase() && s3.nodes && s3.nodes.length && "selector" === s3.nodes[0].type && 0 === s3.nodes[0].nodes.length) return s3.value = ":not", void s3.nodes[0].append(e2.universal());
      if (":-csstools-matches" === s3.value) if (!s3.nodes || s3.nodes.length) {
        if (s3.walkPseudos((s4) => {
          if (e2.isPseudoElement(s4)) {
            let e3 = s4.value;
            if (e3.startsWith("::-csstools-invalid-")) return;
            for (; e3.startsWith(":"); ) e3 = e3.slice(1);
            s4.value = `::-csstools-invalid-${e3}`, t();
          }
        }), 1 === s3.nodes.length && "selector" === s3.nodes[0].type) {
          if (1 === s3.nodes[0].nodes.length) return void s3.replaceWith(s3.nodes[0].nodes[0]);
          if (!s3.nodes[0].some((e3) => "combinator" === e3.type)) return void s3.replaceWith(...s3.nodes[0].nodes);
        }
        1 !== r.nodes.length || "selector" !== r.nodes[0].type || 1 !== r.nodes[0].nodes.length || r.nodes[0].nodes[0] !== s3 ? childAdjacentChild(s3.parent) || isInCompoundWithOneOtherElement(s3.parent) || isPseudoInFirstCompound(s3.parent) || samePrecedingElement(s3.parent) || ("warning" === o2.onComplexSelector && n2(), s3.value = ":is") : s3.replaceWith(...s3.nodes[0].nodes);
      } else s3.remove();
    }), r.walk((e3) => {
      "selector" === e3.type && "nodes" in e3 && 1 === e3.nodes.length && "selector" === e3.nodes[0].type && e3.replaceWith(e3.nodes[0]);
    }), r.walk((e3) => {
      "nodes" in e3 && sortCompoundSelectorsInsideComplexSelector(e3);
    }), r.toString();
  }).filter((e3) => !!e3);
}
function splitSelectors(o2, n2, t = 0) {
  const r = ":not(#" + n2.specificityMatchingName + ")", d = ":not(." + n2.specificityMatchingName + ")", l = ":not(" + n2.specificityMatchingName + ")";
  return o2.flatMap((o3) => {
    if (-1 === o3.toLowerCase().indexOf(":is")) return o3;
    let i = false;
    const a = [];
    if (e2().astSync(o3).walkPseudos((e3) => {
      if (":is" !== e3.value.toLowerCase() || !e3.nodes || !e3.nodes.length) return;
      if ("selector" === e3.nodes[0].type && 0 === e3.nodes[0].nodes.length) return;
      if ("pseudo" === e3.parent?.parent?.type && ":not" === e3.parent?.parent?.value?.toLowerCase()) return void a.push([{ start: e3.parent.parent.sourceIndex, end: e3.parent.parent.sourceIndex + e3.parent.parent.toString().length, option: `:not(${e3.nodes.toString()})` }]);
      if ("pseudo" === e3.parent?.parent?.type && ":has" === e3.parent?.parent?.value?.toLowerCase()) return void (e3.value = ":-csstools-matches");
      let o4 = e3.parent;
      for (; o4; ) {
        if (o4.value && ":is" === o4.value.toLowerCase() && "pseudo" === o4.type) return void (i = true);
        o4 = o4.parent;
      }
      const n3 = selectorSpecificity(e3), t2 = e3.sourceIndex, c2 = t2 + e3.toString().length, p = [];
      e3.nodes.forEach((e4) => {
        const o5 = { start: t2, end: c2, option: "" }, i2 = selectorSpecificity(e4);
        let a2 = e4.toString().trim();
        const u = Math.max(0, n3.a - i2.a), h = Math.max(0, n3.b - i2.b), f = Math.max(0, n3.c - i2.c);
        for (let e5 = 0; e5 < u; e5++) a2 += r;
        for (let e5 = 0; e5 < h; e5++) a2 += d;
        for (let e5 = 0; e5 < f; e5++) a2 += l;
        o5.option = a2, p.push(o5);
      }), a.push(p);
    }), !a.length) return [o3];
    let c = [];
    return cartesianProduct(...a).forEach((e3) => {
      let s = "";
      for (let n3 = 0; n3 < e3.length; n3++) {
        const t2 = e3[n3];
        s += o3.substring(e3[n3 - 1]?.end || 0, e3[n3].start), s += ":-csstools-matches(" + t2.option + ")", n3 === e3.length - 1 && (s += o3.substring(e3[n3].end));
      }
      c.push(s);
    }), i && t < 10 && (c = splitSelectors(c, n2, t + 1)), c;
  }).filter((e3) => !!e3);
}
function cartesianProduct(...e3) {
  const s = [], o2 = e3.length - 1;
  return function helper(n2, t) {
    for (let r = 0, d = e3[t].length; r < d; r++) {
      const d2 = n2.slice(0);
      d2.push(e3[t][r]), t === o2 ? s.push(d2) : helper(d2, t + 1);
    }
  }([], 0), s;
}
var n = /:is\(/i;
var creator = (e3) => {
  const s = { specificityMatchingName: "does-not-exist", ...e3 || {} };
  return { postcssPlugin: "postcss-is-pseudo-class", prepare() {
    const e4 = /* @__PURE__ */ new WeakSet();
    return { postcssPlugin: "postcss-is-pseudo-class", Rule(o2, { result: t }) {
      if (!o2.selector) return;
      if (!n.test(o2.selector)) return;
      if (e4.has(o2)) return;
      let r = false;
      const warnOnComplexSelector = () => {
        "warning" === s.onComplexSelector && (r || (r = true, o2.warn(t, `Complex selectors in '${o2.selector}' can not be transformed to an equivalent selector without ':is()'.`)));
      };
      let d = false;
      const warnOnPseudoElements = () => {
        "warning" === s.onPseudoElement && (d || (d = true, o2.warn(t, `Pseudo elements are not allowed in ':is()', unable to transform '${o2.selector}'`)));
      };
      try {
        let n2 = false;
        const t2 = [], r2 = complexSelectors(splitSelectors(o2.selectors, { specificityMatchingName: s.specificityMatchingName }), { onComplexSelector: s.onComplexSelector }, warnOnComplexSelector, warnOnPseudoElements);
        if (Array.from(new Set(r2)).forEach((s2) => {
          if (o2.selectors.indexOf(s2) > -1) t2.push(s2);
          else {
            if (alwaysValidSelector(s2)) return t2.push(s2), void (n2 = true);
            e4.add(o2), o2.cloneBefore({ selector: s2 }), n2 = true;
          }
        }), t2.length && n2 && (e4.add(o2), o2.cloneBefore({ selectors: t2 })), !s.preserve) {
          if (!n2) return;
          o2.remove();
        }
      } catch (e5) {
        if (!(e5 instanceof Error)) throw e5;
        if (e5.message.indexOf("call stack size exceeded") > -1) throw e5;
        o2.warn(t, `Failed to parse selector "${o2.selector}" with error: ${e5.message}`);
      }
    } };
  } };
};
creator.postcss = true;

// src/plugins/index.ts
import { default as default2 } from "postcss-rem-to-responsive-pixel";
function getPlugins(options) {
  const ctx = createContext();
  options.ctx = ctx;
  const plugins = [
    ...options.postcssOptions?.plugins ?? [],
    postcssWeappTailwindcssPrePlugin(options),
    postcssPresetEnv(options.cssPresetEnv)
  ];
  if (options.rem2rpx) {
    plugins.push(
      postcssRem2rpx(
        typeof options.rem2rpx === "object" ? options.rem2rpx : {
          rootValue: 32,
          propList: ["*"],
          transformUnit: "rpx"
        }
      )
    );
  }
  plugins.push(postcssWeappTailwindcssPostPlugin(options));
  return plugins;
}

// src/preflight.ts
function createInjectPreflight(options) {
  const result = [];
  if (options && typeof options === "object") {
    const entries = Object.entries(options);
    for (const [prop, value] of entries) {
      if (value !== false) {
        result.push({
          prop,
          value: value.toString()
        });
      }
    }
  }
  return () => {
    return result;
  };
}

// src/handler.ts
function styleHandler(rawSource, options) {
  return postcss(
    getPlugins(options)
  ).process(
    rawSource,
    options.postcssOptions?.options ?? {
      from: void 0
    }
  ).async();
}
function createStyleHandler(options) {
  const cachedOptions = defuOverrideArray(
    options,
    getDefaultOptions()
  );
  cachedOptions.cssInjectPreflight = createInjectPreflight(cachedOptions.cssPreflight);
  return (rawSource, opt) => {
    return styleHandler(
      rawSource,
      defuOverrideArray(opt, cachedOptions)
    );
  };
}
export {
  createInjectPreflight,
  createStyleHandler,
  internalCssSelectorReplacer
};
