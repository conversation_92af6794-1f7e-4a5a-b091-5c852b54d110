{"name": "@csstools/postcss-cascade-layers", "description": "Use cascade layers in CSS", "version": "5.0.2", "contributors": [{"name": "<PERSON><PERSON>", "email": "o.ni<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON> J<PERSON>d", "email": "<EMAIL>"}, {"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/selector-specificity": "^5.0.0", "postcss-selector-parser": "^7.0.0"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-cascade-layers#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-cascade-layers"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["cascade", "css", "layers", "postcss", "postcss-plugin", "selectors", "specificity"]}