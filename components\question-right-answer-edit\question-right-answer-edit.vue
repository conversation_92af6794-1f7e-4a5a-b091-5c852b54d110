<template>
     
    <view>
        
      <view   class="mb-5">
    
       
          <view class="flex flex-wrap gap-4">
            <view 
              v-for="(item, index) in radioOptions" 
              :key="index"
              :class="[
                ' py-2 rounded-lg flex items-center  ',
          
              ]"
             
            >
              <radio  
                :checked="questionType==1||questionType==3?value==item.value:value.includes(item.value)"   
                 @click="setRadio(index)"
              >
              <text class="ml-1">{{ item.label }}</text>
              </radio>
             
            </view>
          </view>
     
      </view>
 
     
      
   
    </view>
       
</template>

<script>
export default {
props:{
    questionType:Number,
    options:Array,
    modelValue:String|Number
},
data() {
  return {
     
  }
},
computed:{
    radioOptions(){
       
         if(this.questionType === 1||this.questionType === 2){
            return this.options.map((item,index)=>{
                return {
                    label:String.fromCharCode(65 + index),
                    value:index+''
                }
            })
         }else if(this.questionType === 3){
            return [
                {
                    label:"正确",
                    value:'0'
                },
                {
                    label:"错误",
                    value:'1'
                }
            ]
         }
    },
    value(){
        
        if(this.questionType === 1||this.questionType === 3){
            return this.modelValue
        }else if(this.questionType === 2){
         
           return this.modelValue?this.modelValue.split(","):[]
        }
    }
},
methods: {
    setRadio(index){
      
        // this.$emit("update:value",this.radioOptions[index].value)
        if(this.questionType === 1||this.questionType === 3){
        
            this.$emit("update:modelValue",index+'')
        }else if(this.questionType === 2){

            const checked = this.value;

          
           
            if(checked.includes(index+'')){
                this.$emit("update:modelValue",checked.filter(item=>item!=index).join(","))
            }else{
           
                this.$emit("update:modelValue",[...checked,index+''].join(","))
            }
        }
    }
}
}
</script>

<style scoped>
</style>