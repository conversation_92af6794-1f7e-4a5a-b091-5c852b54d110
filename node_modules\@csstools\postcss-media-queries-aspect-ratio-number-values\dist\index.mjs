import{matchesRatioExactly as e,parse as t,isMediaQueryInvalid as r,cloneMediaQuery as a,isMediaFeaturePlain as n,isMediaFeatureRangeNameValue as o,isMediaFeatureRangeValueName as i,isMediaFeatureRangeValueNameValue as u}from"@csstools/media-query-list-parser";import{isTokenNode as s,isFunctionNode as l,TokenNode as c,FunctionNode as v,SimpleBlockNode as m,WhitespaceNode as d}from"@csstools/css-parser-algorithms";import{isTokenNumber as p,TokenType as f,NumberType as g}from"@csstools/css-tokenizer";const w=1e5,h=2147483647;function transformMediaFeatureValue(t){if(Array.isArray(t.value)&&e(t.value)){const e=[];for(let r=0;r<t.value.length;r++){const a=t.value[r];s(a)&&p(a.value)?e.push(a):l(a)&&"calc"===a.getName().toLowerCase()&&e.push(a)}if(2!==e.length)return;const r=e[0],a=t.value.indexOf(r),n=e[1],o=t.value.indexOf(n);if(s(n)&&p(n.value)&&0===n.value[4].value)return t.value.splice(a,1,new c([f.Number,h.toString(),-1,-1,{value:h,type:g.Integer}])),void t.value.splice(o,1,new c([f.Number,"1",-1,-1,{value:1,type:g.Integer}]));if(s(r)&&p(r.value)&&r.value[4].type===g.Integer&&s(n)&&p(n.value)&&n.value[4].type===g.Integer)return;let i=null,u=null;if(l(r)&&"calc"===r.getName().toLowerCase()){if(r.toString().includes(w.toString()))return;i=modifyCalc(r)}if(l(n)&&"calc"===n.getName().toLowerCase()){if(n.toString().includes(w.toString()))return;u=modifyCalc(n)}if(s(r)&&p(r.value)&&s(n)&&p(n.value)){const e=r.value,t=n.value,a=Math.round(e[4].value*w),o=Math.round(t[4].value*w),s=greatestCommonDivisor(a,o);i=new c([f.Number,Math.round(a/s).toString(),-1,-1,{value:Math.round(a/s),type:g.Integer}]),u=new c([f.Number,Math.round(o/s).toString(),-1,-1,{value:Math.round(o/s),type:g.Integer}])}else{if(s(r)&&p(r.value)){const e=r.value;i=new c([f.Number,Math.round(e[4].value*w).toString(),-1,-1,{value:Math.round(e[4].value*w),type:g.Integer}])}if(s(n)&&p(n.value)){const e=n.value;u=new c([f.Number,Math.round(e[4].value*w).toString(),-1,-1,{value:Math.round(e[4].value*w),type:g.Integer}])}}return i&&u?(t.value.splice(a,1,i),void t.value.splice(o,1,u)):void 0}const r=Array.isArray(t.value)?t.value:[t.value];for(let e=0;e<r.length;e++){const a=r[e];if(s(a)){const n=a.value;if(!p(n))return;if(n[4].type===g.Integer)return r.splice(e+1,0,new c([f.Delim,"/",-1,-1,{value:"/"}]),new c([f.Number,"1",-1,-1,{value:1,type:g.Integer}])),void(t.value=r);if(n[4].type===g.Number){const a=Math.round(n[4].value*w),o=greatestCommonDivisor(a,w);return r.splice(e,1,new c([f.Number,Math.round(a/o).toString(),-1,-1,{value:Math.round(a/o),type:g.Integer}]),new c([f.Delim,"/",-1,-1,{value:"/"}]),new c([f.Number,Math.round(w/o).toString(),-1,-1,{value:Math.round(w/o),type:g.Integer}])),void(t.value=r)}return}if(l(a)&&"calc"===a.getName().toLowerCase())return r.splice(e,1,modifyCalc(a),new c([f.Delim,"/",-1,-1,{value:"/"}]),new c([f.Number,w.toString(),-1,-1,{value:w,type:g.Integer}])),void(t.value=r)}}function modifyCalc(e){return new v([f.Function,"calc(",-1,-1,{value:"calc("}],[f.CloseParen,")",-1,-1,void 0],[new m([f.OpenParen,"(",-1,-1,void 0],[f.CloseParen,")",-1,-1,void 0],e.value),new d([[f.Whitespace," ",-1,-1,void 0]]),new c([f.Delim,"*",-1,-1,{value:"*"}]),new d([[f.Whitespace," ",-1,-1,void 0]]),new c([f.Number,w.toString(),-1,-1,{value:w,type:g.Integer}])])}function greatestCommonDivisor(e,t){if(Number.isNaN(e)||Number.isNaN(t))throw new Error("Unexpected 'NaN' when calculating the greatest common divisor.");if(!Number.isFinite(e)||!Number.isFinite(t))throw new Error("Unexpected 'Infinite' value when calculating the greatest common divisor.");for(t>e&&([e,t]=[t,e]);;){if(0==t)return e;if(0==(e%=t))return t;t%=e}}const y=new Set(["aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio"]);function transformMediaQueryList(e,s){const l=t(e,{preserveInvalidMediaQueries:!0,onParseError:()=>{throw new Error(`Unable to parse media query "${e}"`)}}),c=new Set(l.map((e=>e.toString())));return l.flatMap((e=>{if(r(e))return[e.toString()];const t=a(e);t.walk((e=>{const t=e.node;if(n(t)||o(t)||i(t)){const e=t.name.getName().toLowerCase();if(!y.has(e))return;transformMediaFeatureValue(t.value)}else if(u(t)){const e=t.name.getName().toLowerCase();if(!y.has(e))return;transformMediaFeatureValue(t.valueOne);transformMediaFeatureValue(t.valueTwo)}else;}));const l=e.toString(),v=t.toString();return v===l||c.has(v)?[l]:s?[l,v]:[v]})).join(",")}const creator=e=>{const t=Object.assign({preserve:!1},e);return{postcssPlugin:"postcss-media-queries-aspect-ratio-number-values",AtRule(e,{result:r}){if("media"!==e.name.toLowerCase())return;const a=e.params.toLowerCase();if(!(a.includes("aspect-ratio")||a.includes("min-aspect-ratio")||a.includes("max-aspect-ratio")||a.includes("device-aspect-ratio")||a.includes("min-device-aspect-ratio")||a.includes("max-device-aspect-ratio")))return;let n;try{if(n=transformMediaQueryList(e.params,t.preserve),n===e.params)return}catch(t){return void e.warn(r,`Failed to transform @media params for "${e.params}" with message: "${t instanceof Error?t.message:t}"`)}e.cloneBefore({params:n}),e.remove()}}};creator.postcss=!0;export{creator as default};
