import type { PluginCreator } from 'postcss';

declare const creator: PluginCreator<pluginOptions>;
export default creator;

export declare enum DirectionFlow {
    TopToBottom = "top-to-bottom",
    BottomToTop = "bottom-to-top",
    RightToLeft = "right-to-left",
    LeftToRight = "left-to-right"
}

/** postcss-logical-overscroll-behavior plugin options */
export declare type pluginOptions = {
    /** Sets the direction for inline. default: left-to-right */
    inlineDirection?: DirectionFlow;
};

export { }
