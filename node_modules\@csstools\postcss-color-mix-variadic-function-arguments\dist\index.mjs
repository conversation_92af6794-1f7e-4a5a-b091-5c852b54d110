import s from"@csstools/postcss-progressive-custom-properties";import{color as e,SyntaxFlag as r,serializeRGB as t,colorDataFitsRGB_Gamut as o,serializeP3 as i}from"@csstools/css-color-parser";import{hasFallback as a,hasSupportsAtRuleAncestor as n}from"@csstools/utilities";import{replaceComponentValues as c,parseCommaSeparatedListOfComponentValues as l,isFunctionNode as u,stringify as p}from"@csstools/css-parser-algorithms";import{tokenize as m}from"@csstools/css-tokenizer";const f=/\bcolor-mix\(/i,g=/^color-mix$/i,basePlugin=s=>({postcssPlugin:"color-mix-variadic-function-arguments",Declaration(v){const b=v.value;if(!f.test(b))return;if(a(v))return;if(n(v,f))return;const d=m({css:b}),x=c(l(d),(s=>{if(!u(s)||!g.test(s.getName()))return;const o=e(s);return o&&!o.syntaxFlags.has(r.Experimental)&&o.syntaxFlags.has(r.ColorMixVariadic)?t(o):void 0})),P=p(x);if(P===b)return;let F=P;s?.subFeatures.displayP3&&(F=p(c(l(d),(s=>{if(!u(s)||!g.test(s.getName()))return;const a=e(s);return a&&!a.syntaxFlags.has(r.Experimental)?o(a)?t(a):i(a):void 0})))),v.cloneBefore({value:P}),s?.subFeatures.displayP3&&F!==P&&v.cloneBefore({value:F}),s?.preserve||v.remove()}});basePlugin.postcss=!0;const postcssPlugin=e=>{const r=Object.assign({enableProgressiveCustomProperties:!0,preserve:!1,subFeatures:{displayP3:!0}},e);return r.subFeatures=Object.assign({displayP3:!0},r.subFeatures),r.enableProgressiveCustomProperties&&(r.preserve||r.subFeatures.displayP3)?{postcssPlugin:"color-mix-variadic-function-arguments",plugins:[s(),basePlugin(r)]}:basePlugin(r)};postcssPlugin.postcss=!0;export{postcssPlugin as default};
