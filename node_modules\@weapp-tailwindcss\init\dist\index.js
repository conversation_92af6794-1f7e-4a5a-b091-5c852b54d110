"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { newObj[key] = obj[key]; } } } newObj.default = obj; return newObj; } } function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }// src/index.ts
var _process = require('process'); var _process2 = _interopRequireDefault(_process);
var _logger = require('@weapp-tailwindcss/logger');
var _shared = require('@weapp-tailwindcss/shared');
var _fsextra = require('fs-extra'); var _fsextra2 = _interopRequireDefault(_fsextra);
var _pathe = require('pathe'); var _pathe2 = _interopRequireDefault(_pathe);

// src/npm.ts

function fetchPackage(packageName, options) {
  const opts = _shared.defu.call(void 0, options, {
    // 默认使用国内镜像地址
    registry: "https://registry.npmmirror.com"
  });
  return Promise.resolve().then(() => _interopRequireWildcard(require("npm-registry-fetch"))).then(({ json }) => {
    return json(`/${packageName}`, opts);
  });
}
async function getLatestVersionInRange(packageName, versionRange, options) {
  const response = await fetchPackage(packageName, options);
  const versions = Object.keys(response.versions);
  const filteredVersions = versions.filter((version) => version.startsWith(versionRange));
  return filteredVersions[filteredVersions.length - 1];
}
var defaultDevDeps = {
  "tailwindcss": "3",
  "postcss": "8",
  "autoprefixer": "10",
  "weapp-tailwindcss": "3"
};
async function getDevDepsVersions(options) {
  return Object.fromEntries(await Promise.all(
    Object.entries(defaultDevDeps).map(
      async (x) => {
        return [x[0], `^${await getLatestVersionInRange(...x, options)}`];
      }
    )
  ));
}

// src/index.ts
async function createContext(options) {
  const { cwd, pkgJsonBasename, postcssConfigBasename, tailwindConfigBasename, fetchOptions } = options;
  const pkgJsonPath = _pathe2.default.resolve(cwd, pkgJsonBasename);
  if (await _fsextra2.default.exists(pkgJsonPath)) {
    const pkgJson = await _fsextra2.default.readJson(pkgJsonPath);
    const versions = await getDevDepsVersions(fetchOptions);
    return {
      pkgJson,
      pkgJsonPath,
      cwd,
      versions,
      postcssConfigBasename,
      tailwindConfigBasename,
      get type() {
        return pkgJson.type;
      }
    };
  } else {
    _logger.logger.warn("\u5F53\u524D\u76EE\u5F55\u4E0B\u4E0D\u5B58\u5728 `package.json` \u6587\u4EF6\uFF0C\u521D\u59CB\u5316\u811A\u672C\u5C06\u88AB\u8DF3\u8FC7\uFF0C\u8BF7\u6267\u884C `npm init` \u6216\u624B\u52A8\u521B\u5EFA `package.json` \u540E\u91CD\u8BD5 ");
  }
}
async function updatePackageJson(ctx) {
  _shared.setValue.call(void 0, ctx.pkgJson, "scripts.postinstall", "weapp-tw patch");
  for (const [key, value] of Object.entries(ctx.versions)) {
    _shared.setValue.call(void 0, ctx.pkgJson, `devDependencies.${key}`, value);
  }
  await _fsextra2.default.writeJSON(ctx.pkgJsonPath, ctx.pkgJson, { spaces: 2 });
}
async function touchPostcssConfig(ctx) {
  const data = `${ctx.type === "module" ? "export default " : "module.exports = "}{
  plugins: {
    tailwindcss: {},
    // \u5047\u5982\u6846\u67B6\u5DF2\u7ECF\u5185\u7F6E\u4E86 \`autoprefixer\`\uFF0C\u53EF\u4EE5\u53BB\u9664\u4E0B\u4E00\u884C
    autoprefixer: {},
  },
}
`;
  await _fsextra2.default.writeFile(_pathe2.default.resolve(ctx.cwd, ctx.postcssConfigBasename), data);
}
async function touchTailwindConfig(ctx) {
  const data = `/** @type {import('tailwindcss').Config} */
${ctx.type === "module" ? "export default " : "module.exports = "}{
  // \u8FD9\u91CC\u7ED9\u51FA\u4E86\u4E00\u4EFD uni-app /taro \u901A\u7528\u793A\u4F8B\uFF0C\u5177\u4F53\u8981\u6839\u636E\u4F60\u81EA\u5DF1\u9879\u76EE\u7684\u76EE\u5F55\u7ED3\u6784\u8FDB\u884C\u914D\u7F6E
  // \u4E0D\u5728 content \u5305\u62EC\u7684\u6587\u4EF6\u5185\uFF0C\u4F60\u7F16\u5199\u7684 class\uFF0C\u662F\u4E0D\u4F1A\u751F\u6210\u5BF9\u5E94\u7684css\u5DE5\u5177\u7C7B\u7684
  content: ['./public/index.html', './src/**/*.{wxml,html,js,ts,jsx,tsx,vue}'],
  // \u5176\u4ED6\u914D\u7F6E\u9879
  // ...
  corePlugins: {
    // \u5C0F\u7A0B\u5E8F\u4E0D\u9700\u8981 preflight \u548C container\uFF0C\u56E0\u4E3A\u8FD9\u4E3B\u8981\u662F\u7ED9 h5 \u7684\uFF0C\u5982\u679C\u4F60\u8981\u540C\u65F6\u5F00\u53D1\u5C0F\u7A0B\u5E8F\u548C h5 \u7AEF\uFF0C\u4F60\u5E94\u8BE5\u4F7F\u7528\u73AF\u5883\u53D8\u91CF\u6765\u63A7\u5236\u5B83
    preflight: false,
    container: false,
  },
}
`;
  await _fsextra2.default.writeFile(_pathe2.default.resolve(ctx.cwd, ctx.tailwindConfigBasename), data);
}
function getInitDefaults() {
  return {
    cwd: _process2.default.cwd(),
    postcssConfigBasename: "postcss.config.js",
    tailwindConfigBasename: "tailwind.config.js",
    pkgJsonBasename: "package.json"
  };
}
async function init(options) {
  const opts = _shared.defu.call(void 0, options, getInitDefaults());
  const ctx = await createContext(opts);
  if (ctx) {
    await updatePackageJson(ctx);
    _logger.logger.success("`package.json` \u6587\u4EF6\u4FEE\u6539\u5B8C\u6210\uFF01");
    await touchPostcssConfig(ctx);
    _logger.logger.success("`postcss.config.js` \u6587\u4EF6\u521B\u5EFA\u5B8C\u6210\uFF01");
    await touchTailwindConfig(ctx);
    _logger.logger.success("`tailwind.config.js` \u6587\u4EF6\u521B\u5EFA\u5B8C\u6210\uFF01");
    _logger.logger.success("`weapp-tailwindcss` \u521D\u59CB\u5316\u5B8C\u6210\uFF01\u8BF7\u6839\u636E\u4F60\u81EA\u5B9A\u4E49\u7684\u9700\u6C42\uFF0C\u66F4\u6539\u5BF9\u5E94\u7684\u914D\u7F6E\u6587\u4EF6(\u6BD4\u5982 `tailwind.config.js` \u4E2D\u7684 `content` \u914D\u7F6E)");
  }
}







exports.createContext = createContext; exports.getInitDefaults = getInitDefaults; exports.init = init; exports.touchPostcssConfig = touchPostcssConfig; exports.touchTailwindConfig = touchTailwindConfig; exports.updatePackageJson = updatePackageJson;
