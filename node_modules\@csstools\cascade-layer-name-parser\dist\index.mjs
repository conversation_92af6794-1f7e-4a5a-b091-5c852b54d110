import{isTokenIdent as e,TokenType as t,isTokenDelim as r,stringify as n,tokenize as a,ParseError as s,isTokenWhiteSpaceOrComment as o,isTokenWhitespace as l,isTokenComment as i}from"@csstools/css-tokenizer";import{parseCommaSeparatedListOfComponentValues as c,isTokenNode as m,isCommentNode as u,isWhitespaceNode as p}from"@csstools/css-parser-algorithms";class LayerName{parts;constructor(e){this.parts=e}tokens(){return[...this.parts]}slice(t,r){const n=[];for(let t=0;t<this.parts.length;t++)e(this.parts[t])&&n.push(t);const a=n.slice(t,r);return new LayerName(this.parts.slice(a[0],a[a.length-1]+1))}concat(n){const a=[t.Delim,".",-1,-1,{value:"."}];return new LayerName([...this.parts.filter((t=>e(t)||r(t))),a,...n.parts.filter((t=>e(t)||r(t)))])}segments(){return this.parts.filter((t=>e(t))).map((e=>e[4].value))}name(){return this.parts.filter((t=>e(t)||r(t))).map((e=>e[1])).join("")}equal(e){const t=this.segments(),r=e.segments();if(t.length!==r.length)return!1;for(let e=0;e<t.length;e++){if(t[e]!==r[e])return!1}return!0}toString(){return n(...this.parts)}toJSON(){return{parts:this.parts,segments:this.segments(),name:this.name()}}}function addLayerToModel(e,t){t.forEach((t=>{const r=t.segments();e:for(let n=0;n<r.length;n++){const r=t.slice(0,n+1),a=r.segments();let s=-1,o=0;for(let t=0;t<e.length;t++){const r=e[t].segments();let n=0;t:for(let e=0;e<r.length;e++){const t=r[e],s=a[e];if(s===t&&e+1===a.length)continue e;if(s!==t){if(s!==t)break t}else n++}n>=o&&(s=t,o=n)}-1===s?e.push(r):e.splice(s+1,0,r)}}))}function parseFromTokens(t,n){const a=c(t,{onParseError:n?.onParseError}),f=n?.onParseError??(()=>{}),h=["6.4.2. Layer Naming and Nesting","Layer name syntax","<layer-name> = <ident> [ '.' <ident> ]*"],d=t[0][2],y=t[t.length-1][3],g=[];for(let t=0;t<a.length;t++){const n=a[t];for(let e=0;e<n.length;e++){const t=n[e];if(!m(t)&&!u(t)&&!p(t))return f(new s(`Invalid cascade layer name. Invalid layer name part "${t.toString()}"`,d,y,h)),[]}const c=n.flatMap((e=>e.tokens()));let w=!1,v=!1,L=null;for(let t=0;t<c.length;t++){const n=c[t];if(!(o(n)||e(n)||r(n)&&"."===n[4].value))return f(new s(`Invalid cascade layer name. Invalid character "${n[1]}"`,d,y,h)),[];if(!w&&r(n))return f(new s("Invalid cascade layer name. Layer names can not start with a dot.",d,y,h)),[];if(w){if(l(n)){v=!0;continue}if(v&&i(n))continue;if(v)return f(new s("Invalid cascade layer name. Encountered unexpected whitespace between layer name parts.",d,y,h)),[];if(e(L)&&e(n))return f(new s("Invalid cascade layer name. Layer name parts must be separated by dots.",d,y,h)),[];if(r(L)&&r(n))return f(new s("Invalid cascade layer name. Layer name parts must not be empty.",d,y,h)),[]}e(n)&&(w=!0),(e(n)||r(n))&&(L=n)}if(!L)return f(new s("Invalid cascade layer name. Empty layer name.",d,y,h)),[];if(r(L))return f(new s("Invalid cascade layer name. Layer name must not end with a dot.",d,y,h)),[];g.push(new LayerName(c))}return g}function parse(e,t){return parseFromTokens(a({css:e},{onParseError:t?.onParseError}),t)}export{LayerName,addLayerToModel,parse,parseFromTokens};
