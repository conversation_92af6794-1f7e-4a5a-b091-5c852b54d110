<template>
  <scroll-view
    scroll-y
    class="h-full overflow-hidden"
    @scrolltolower="onScrollToLower"
    :refresher-enabled="enableRefresh"
    :refresher-triggered="refreshing"
    @refresherrefresh="onRefresh"
  >
    <view  >
      <slot :list="list" ></slot>
      <view v-if="loading" class="text-center py-3 text-gray-400 text-sm flex items-center justify-center">
        <text class="fas fa-circle-notch fa-spin mr-2 text-lg"></text>
        加载中...
      </view>
      <view v-else-if="!list.length && !loading" >
        <slot name="empty"></slot>
        <view class="text-center py-8 text-gray-400 text-sm" v-if="!$slots.empty">
            <text  >暂无数据</text>
        </view>
      
       </view>
      <view v-else-if="finished" class="text-center py-3 text-gray-300 text-xs">没有更多了</view>
    </view>
  </scroll-view>
</template>

<script>
let scrollDebounceTimer = null;
export default {
  name: 'scroll-pagination',
  props: {
    pageSize: { type: Number, default: 20 },
    autoLoad: { type: Boolean, default: true },
    enableRefresh: { type: Boolean, default: false }
  },
  data() {
    return {
        
      list: [],
      pageNo: 1,
      loading: false,
      finished: false,
      refreshing: false
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.pageNo = 1;
      this.finished = false;
      this.list = [];
      await this.loadMore();
    },
    async loadMore() {
      if (this.loading || this.finished) return;
      this.loading = true;
      try {
        await new Promise((resolve) => {
          this.$emit('load', { pageNo: this.pageNo, pageSize: this.pageSize }, (res) => {
            const data = Array.isArray(res) ? res : (res.list || []);
            if (this.pageNo === 1) {
              this.list = data;
            } else {
              this.list = this.list.concat(data);
            }
            if (!data.length || (res.total && this.list.length >= res.total)) {
              this.finished = true;
            } else {
              this.pageNo++;
            }
            resolve();
          });
        });
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },
    async onRefresh() {
      if (!this.enableRefresh) return;
      this.refreshing = true;
      this.pageNo = 1;
      this.finished = false;
      await this.loadMore();
    },
    onScrollToLower() {
      // 防抖，300ms内只触发一次
      if (scrollDebounceTimer) clearTimeout(scrollDebounceTimer);
      scrollDebounceTimer = setTimeout(() => {
        if (this.autoLoad) {
          this.loadMore();
        }
      }, 300);
    },
    
    // 新增：刷新当前页
    refreshCurrentPage() {
      if (this.loading) return;
      this.loading = true;
      this.$emit('load', { pageNo: this.pageNo, pageSize: this.pageSize }, (res) => {
        const data = Array.isArray(res) ? res : (res.list || []);
        const start = (this.pageNo - 1) * this.pageSize;
        // 替换当前页数据
        this.list.splice(start, data.length, ...data);
        this.loading = false;
      });
    },
    // 新增：刷新指定页
    refreshPage(pageNo) {
      if (this.loading) return;
      this.loading = true;
      this.$emit('load', {  pageNo, pageSize: this.pageSize }, (res) => {
        const data = Array.isArray(res) ? res : (res.list || []);
        const start = (pageNo - 1) * this.pageSize;
        this.list.splice(start, data.length, ...data);
        this.loading = false;
      });
    },
    refreshPageByIndex(index) {
      //这里的Index是数据中的索引
      let pageNo = Math.floor(index / this.pageSize) + 1;
      if (this.loading) return;
      this.loading = true;
      this.$emit('load', {  pageNo: pageNo, pageSize: this.pageSize }, (res) => {
        const data = Array.isArray(res) ? res : (res.list || []);
        const start = (pageNo - 1) * this.pageSize;
        this.list.splice(start, data.length, ...data);
        this.loading = false;
      });
    }
  }
}
</script>

<style scoped>
</style> 