"use strict";var s=require("@csstools/postcss-progressive-custom-properties"),e=require("@csstools/css-color-parser"),t=require("@csstools/utilities"),a=require("@csstools/css-parser-algorithms"),r=require("@csstools/css-tokenizer");const o=/\b(?:oklab|oklch)\(/i,l=/^(?:oklab|oklch)$/i,basePlugin=s=>({postcssPlugin:"postcss-oklab-function",Declaration(n){const i=n.value;if(!o.test(i))return;if(t.hasFallback(n))return;if(t.hasSupportsAtRuleAncestor(n,o))return;const u=r.tokenize({css:i}),c=a.replaceComponentValues(a.parseCommaSeparatedListOfComponentValues(u),(s=>{if(!a.isFunctionNode(s)||!l.test(s.getName()))return;const t=e.color(s);return t&&!(t.syntaxFlags.has(e.SyntaxFlag.Experimental)||t.syntaxFlags.has(e.SyntaxFlag.HasNoneKeywords)||t.syntaxFlags.has(e.SyntaxFlag.RelativeColorSyntax))?e.serializeRGB(t):void 0})),p=a.stringify(c);if(p===i)return;let g=p;s?.subFeatures.displayP3&&(g=a.stringify(a.replaceComponentValues(a.parseCommaSeparatedListOfComponentValues(u),(s=>{if(!a.isFunctionNode(s)||!l.test(s.getName()))return;const t=e.color(s);return t&&!(t.syntaxFlags.has(e.SyntaxFlag.Experimental)||t.syntaxFlags.has(e.SyntaxFlag.HasNoneKeywords)||t.syntaxFlags.has(e.SyntaxFlag.RelativeColorSyntax))?e.colorDataFitsRGB_Gamut(t)?e.serializeRGB(t):e.serializeP3(t):void 0})))),n.cloneBefore({value:p}),s?.subFeatures.displayP3&&g!==p&&n.cloneBefore({value:g}),s?.preserve||n.remove()}});basePlugin.postcss=!0;const postcssPlugin=e=>{const t=Object.assign({enableProgressiveCustomProperties:!0,preserve:!1,subFeatures:{displayP3:!0}},e);return t.subFeatures=Object.assign({displayP3:!0},t.subFeatures),t.enableProgressiveCustomProperties&&(t.preserve||t.subFeatures.displayP3)?{postcssPlugin:"postcss-oklab-function",plugins:[s(),basePlugin(t)]}:basePlugin(t)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
