declare const SYMBOL_TABLE: {
    readonly BACKQUOTE: "`";
    readonly TILDE: "~";
    readonly EXCLAM: "!";
    readonly AT: "@";
    readonly NUMBERSIGN: "#";
    readonly DOLLAR: "$";
    readonly PERCENT: "%";
    readonly CARET: "^";
    readonly AMPERSAND: "&";
    readonly ASTERISK: "*";
    readonly PARENLEFT: "(";
    readonly PARENRIGHT: ")";
    readonly MINUS: "-";
    readonly UNDERSCORE: "_";
    readonly EQUAL: "=";
    readonly PLUS: "+";
    readonly BRACKETLEFT: "[";
    readonly BRACELEFT: "{";
    readonly BRACKETRIGHT: "]";
    readonly BRACERIGHT: "}";
    readonly SEMICOLON: ";";
    readonly COLON: ":";
    readonly QUOTE: "'";
    readonly DOUBLEQUOTE: "\"";
    readonly BACKSLASH: "\\";
    readonly BAR: "|";
    readonly COMMA: ",";
    readonly LESS: "<";
    readonly PERIOD: ".";
    readonly GREATER: ">";
    readonly SLASH: "/";
    readonly QUESTION: "?";
    readonly SPACE: " ";
    readonly DOT: ".";
    readonly HASH: "#";
};
type SYMBOL_TABLE_TYPE = typeof SYMBOL_TABLE;
type SYMBOL_TABLE_TYPE_VALUES = SYMBOL_TABLE_TYPE[keyof SYMBOL_TABLE_TYPE];
type MappingStringDictionary = Record<Exclude<SYMBOL_TABLE_TYPE_VALUES, '-' | '_' | ' '>, string>;
declare const ComplexMappingChars2String: MappingStringDictionary;
declare const ComplexMappingChars2StringEntries: [string, string][];
declare const MappingChars2String: MappingStringDictionary;
declare const MappingChars2StringEntries: [string, string][];
declare const MAX_ASCII_CHAR_CODE = 127;

interface EscapeOptions {
    map?: Record<string, string>;
    ignoreHead?: boolean;
}

declare function isAsciiNumber(code: number): boolean;
declare function isAllowedClassName(className: string): boolean;
declare function escape(selectors: string, options?: EscapeOptions): string;

export { ComplexMappingChars2String, ComplexMappingChars2StringEntries, type EscapeOptions, MAX_ASCII_CHAR_CODE, MappingChars2String, MappingChars2StringEntries, type MappingStringDictionary, SYMBOL_TABLE, type SYMBOL_TABLE_TYPE, type SYMBOL_TABLE_TYPE_VALUES, escape, isAllowedClassName, isAsciiNumber };
