// src/config.ts
import { createDefineConfig, loadConfig } from "c12";
import fs from "fs-extra";
import path from "pathe";

// src/constants.ts
var CONFIG_NAME = "tailwindcss-mangle";

// src/defaults.ts
import process from "node:process";
import { defaultMangleClassFilter } from "@tailwindcss-mangle/shared";
import { CSS_LANGS_RE } from "is-css-request";
var defaultPipelineInclude = [
  /\.(html|js|ts|jsx|tsx|vue|svelte|astro|elm|php|phtml|mdx|md)(?:$|\?)/,
  CSS_LANGS_RE
];
var defaultPipelineExclude = [];
function getDefaultPatchConfig() {
  return {
    output: {
      filename: ".tw-patch/tw-class-list.json",
      removeUniversalSelector: true,
      loose: true
    },
    tailwindcss: {}
  };
}
function getDefaultMangleUserConfig() {
  return {
    mangleClassFilter: defaultMangleClassFilter,
    include: defaultPipelineInclude,
    exclude: defaultPipelineExclude,
    disabled: process.env.NODE_ENV === "development",
    classListPath: ".tw-patch/tw-class-list.json",
    classMapOutput: {
      enable: false,
      filename: ".tw-patch/tw-map-list.json",
      loose: true
    },
    preserveFunction: []
  };
}
function getDefaultUserConfig() {
  return {
    patch: getDefaultPatchConfig(),
    mangle: getDefaultMangleUserConfig()
  };
}

// src/config.ts
function getConfig(cwd) {
  return loadConfig({
    name: CONFIG_NAME,
    defaults: {
      ...getDefaultUserConfig()
    },
    cwd
  });
}
var defineConfig = createDefineConfig();
function initConfig(cwd) {
  return fs.outputFile(
    path.resolve(cwd, `${CONFIG_NAME}.config.ts`),
    `import { defineConfig } from 'tailwindcss-patch'

export default defineConfig({})
`,
    "utf8"
  );
}
export {
  CONFIG_NAME,
  defineConfig,
  getConfig,
  getDefaultMangleUserConfig,
  getDefaultPatchConfig,
  getDefaultUserConfig,
  initConfig
};
