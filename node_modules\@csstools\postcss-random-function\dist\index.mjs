import{calc as e}from"@csstools/css-calc";const o=String.fromCodePoint(0);function randomCacheKeyFromPostcssDeclaration(e){let r="",t=e.parent;for(;t;){switch(t.type){case"rule":r+=o+"selector"+o+t.selector+o;break;case"atrule":"scope"===t.name&&(r+=o+"prelude"+o+t.params+o)}t=t.parent}return{propertyName:e.prop,propertyN:0,elementID:r,documentID:e.source?.input.css??e.root().toString()}}const r=/(?<![-\w])(?:random)\(/i,creator=o=>{const t=Object.assign({preserve:!1},o);return{postcssPlugin:"postcss-random-function",Declaration(o){if(!r.test(o.value))return;const s=e(o.value,{precision:5,toCanonicalUnits:!0,randomCaching:randomCacheKeyFromPostcssDeclaration(o)});s!==o.value&&(o.cloneBefore({value:s}),t.preserve||o.remove())}}};creator.postcss=!0;export{creator as default};
