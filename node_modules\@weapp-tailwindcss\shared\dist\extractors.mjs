import {
  init_esm_shims
} from "./chunk-ELCZ4BFQ.mjs";

// src/extractors/index.ts
init_esm_shims();

// src/extractors/split.ts
init_esm_shims();
var validateFilterRE = /[\w\u00A0-\uFFFF%-?]/;
function isValidSelector(selector = "") {
  return validateFilterRE.test(selector);
}
function splitCode(code, allowDoubleQuotes = false) {
  const splitter = allowDoubleQuotes ? /\s+/ : /\s+|"/;
  return code.split(splitter).filter((element) => isValidSelector(element));
}
export {
  isValidSelector,
  splitCode,
  validateFilterRE
};
