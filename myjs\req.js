import {
	showLoading,
	hideLoading
} from './tips.js'
 
import myConfig from '../config';
const apiServer = myConfig.apiServer;
import store from '../store/index.js';


function decode(str) {
	if (!str) {
		return "";
	}
	var len = str.length;
	var i = 0;
	var res = [];
	var table =
		"LKMNOPQRSTABCDEFGHIJUVWXYZabcdopqrstuvwxefghijklmnyz0123456789+/";
	while (i < len) {
		var code1 = table.indexOf(str.charAt(i++));
		var code2 = table.indexOf(str.charAt(i++));
		var code3 = table.indexOf(str.charAt(i++));
		var code4 = table.indexOf(str.charAt(i++));

		var c1 = (code1 << 2) | (code2 >> 4);
		var c2 = ((code2 & 0xf) << 4) | (code3 >> 2);
		var c3 = ((code3 & 0x3) << 6) | code4;

		res.push(String.fromCharCode(c1));

		if (code3 != 64) {
			res.push(String.fromCharCode(c2));
		}
		if (code4 != 64) {
			res.push(String.fromCharCode(c3));
		}
	}

	function UTF8ToUTF16(str) {
		var res = [],
			len = str.length;
		var i = 0;
		for (var i = 0; i < len; i++) {
			var code = str.charCodeAt(i);
			// 对第一个字节进行判断
			if (((code >> 7) & 0xff) == 0x0) {
				// 单字节
				// 0xxxxxxx
				res.push(str.charAt(i));
			} else if (((code >> 5) & 0xff) == 0x6) {
				// 双字节
				// 110xxxxx 10xxxxxx
				var code2 = str.charCodeAt(++i);
				var byte1 = (code & 0x1f) << 6;
				var byte2 = code2 & 0x3f;
				var utf16 = byte1 | byte2;
				res.push(String.fromCharCode(utf16));
			} else if (((code >> 4) & 0xff) == 0xe) {
				// 三字节
				// 1110xxxx 10xxxxxx 10xxxxxx
				var code2 = str.charCodeAt(++i);
				var code3 = str.charCodeAt(++i);
				var byte1 = (code << 4) | ((code2 >> 2) & 0x0f);
				var byte2 = ((code2 & 0x03) << 6) | (code3 & 0x3f);
				var utf16 = ((byte1 & 0x00ff) << 8) | byte2;
				res.push(String.fromCharCode(utf16));
			} else if (((code >> 3) & 0xff) == 0x1e) {
				// 四字节
				// 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
			} else if (((code >> 2) & 0xff) == 0x3e) {
				// 五字节
				// 111110xx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
			} /** if (((code >> 1) & 0xFF) == 0x7E)*/
			else {
				// 六字节
				// 1111110x 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
			}
		}

		return res.join("");
	}

	return UTF8ToUTF16(res.join(""));
}

export function getHeaders() {
	 
	const token = uni.getStorageSync('h5_token')
	return {
		Authorization:`Bearer ${token}`
	}
}


export function reqWxCodeLogin(callback){
	// #ifdef MP-WEIXIN
	console.log(1)
	
	uni.login({
		error: (err) => {
			console.log("login err", err);
		},
		success: (res) => {
		 
				get(apiServer+"/front/edu/auth/wxMaLogin", 
					 {
						code: res.code
					}
				).then((res)=>{
				 
					if(res.success){
						callback && callback()
					 
						store.commit('login/login', res.data)
					}else{
						store.commit('login/logout')
					}
				})
				 

		},
	})
	// #endif
}

 

  function get(url, data, headers) {
	return new Promise((resolve, reject) => {
		uni.request({
			url: url,
			data: data,
			method: "GET",
			dataType: "json",
			header: headers,
			success: (res) => {
				if (typeof res.data === 'string') {
					try {
						res.data = JSON.parse(decode(res.data))
					} catch (e) {

					}
				}

	 

				resolve(res.data);
			},
			fail: () => {
				uni.showToast({
					title: "网络请求失败",
					icon: "none"
				});
				reject(new Error("网络请求失败"));
			}
		});
	});
}

 function post(url, data, headers, callback) {
	// switch (contentType) {
	// 	case "form":
	// 		headerObj = {
	// 			'content-type': 'application/x-www-form-urlencoded'
	// 		};
	// 		break;
	// 	case "json":
	// 		var headerObj = {
	// 			'content-type': 'application/json'
	// 		};
	// 		break;
	// 	default:
	// 		var headerObj = {
	// 			'content-type': 'application/json'
	// 		};
	// }
	// for (var k in headers) {
	// 	headerObj[k] = headers[k];
	// }
	return new Promise((resolve, reject) => {
		uni.request({
			url: url,
			data: data,
			method: "POST",
			dataType: "json",
			header: headers,
			success: (res) => {
				if (typeof res.data === 'string') {
					try {
						res.data = JSON.parse(decode(res.data))
					} catch (e) {

					}
				}

	 

				resolve(res.data);
			},
			fail: () => {
				uni.showToast({
					title: "网络请求失败",
					icon: "none"
				});
				reject(new Error("网络请求失败"));
			}
		});
	});
}

export async function reqGet(url, data, loading, loadingText) {
	if (!url.startsWith("https://") && !url.startsWith("http://")) {
		url = apiServer + url;
	}

	if (data) {
		for (let i in data) {
			if (data[i] === undefined) {
				delete data[i];
			}
		}
	}

	let headers = getHeaders();

	if (loading) {
		showLoading(loadingText)
	}

	try {
		let res = await get(url, data, headers);
		if (loading) {
			hideLoading();
		}
		const errorCode = res.errorCode;

		// 返回 401 未登录或者登录失效，去登录，登录成功再调一次请求
		if (errorCode == 401) {
			await new Promise((resolve) => {
				reqWxCodeLogin(() => {
					resolve();
				});
			});
			headers = getHeaders();
			res = await get(url, data, headers);
		}
		return res;
	} catch (err) {
		if (loading) {
			hideLoading();
		}
		throw err;
	}
}

export async function reqPost(url, data, loading, loadingText ) {
	if (!url.startsWith("https://") && !url.startsWith("http://")) {
		url = apiServer + url;
	}
	if (data) {
		for (let i in data) {
			if (data[i] === undefined) {
				delete data[i];
			}
		}
	}

	let headers = getHeaders();

	if (loading) {
		showLoading(loadingText)
	}

	try {
		let res = await post(url, data, headers);
		if (loading) {
			hideLoading();
		}
		if (res.errorCode == '401') {
			await new Promise((resolve) => {
				reqWxCodeLogin(() => {
					resolve();
				});
			});
			headers = getHeaders();
			res = await post(url, data, headers);
		}
		return res;
	} catch (err) {
		if (loading) {
			hideLoading();
		}
		throw err;
	}
}
