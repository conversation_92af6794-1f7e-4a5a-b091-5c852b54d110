import e from"postcss-selector-parser";import t from"postcss-value-parser";import{selectorSpecificity as s}from"@csstools/selector-specificity";function encodeCSS(e){if(""===e)return"";let t,s="";for(let o=0;o<e.length;o++)t=e.charCodeAt(o).toString(36),s+=0===o?t:"-"+t;return"csstools-has-"+s}function isGuardedByAtSupportsFromAtRuleParams(e){if(!e.toLowerCase().includes(":has("))return!1;let s=!1;try{const o=new Set;t(e).walk((e=>{if("function"===e.type&&"selector"===e.value.toLowerCase())return o.add(t.stringify(e.nodes)),!1})),o.forEach((e=>{selectorContainsHasPseudo(e)&&(s=!0)}))}catch{}return s}function selectorContainsHasPseudo(t){if(!t.toLowerCase().includes(":has("))return!1;let s=!1;try{e().astSync(t).walk((e=>{if("pseudo"===e.type&&":has"===e.value.toLowerCase()&&e.nodes&&e.nodes.length>0)return s=!0,!1}))}catch{}return s}const creator=t=>{const o={preserve:!0,specificityMatchingName:"does-not-exist",...t||{}},r=":not(#"+o.specificityMatchingName+")",n=":not(."+o.specificityMatchingName+")",a=":not("+o.specificityMatchingName+")";return{postcssPlugin:"css-has-pseudo",prepare(){const t=new WeakSet;return{postcssPlugin:"css-has-pseudo",RuleExit(c,{result:i}){if(t.has(c))return;if(!c.selector.toLowerCase().includes(":has(")||isWithinSupportCheck(c))return;const l=c.selectors.map((t=>{if(!t.toLowerCase().includes(":has("))return t;let l;try{l=e().astSync(t)}catch(e){return c.warn(i,`Failed to parse selector : "${t}" with message: "${e instanceof Error?e.message:e}"`),t}if(void 0===l)return t;l.walkPseudos((t=>{let s=t.parent,r=!1;for(;s;)e.isPseudoClass(s)&&":has"===s.value.toLowerCase()&&(r=!0),s=s.parent;r&&(":visited"===t.value.toLowerCase()&&t.replaceWith(e.className({value:o.specificityMatchingName})),":any-link"===t.value.toLowerCase()&&(t.value=":link"))})),l.walkPseudos((t=>{if(":has"!==t.value.toLowerCase()||!t.nodes)return;const o=t.parent;if(!o)return;const c=e.selector({value:"",nodes:[]});{let t=o.nodes.length;e:for(let s=0;s<o.nodes.length;s++){const r=o.nodes[s];if(e.isPseudoElement(r))for(let e=s-1;e>=0;e--)if("combinator"!==o.nodes[s].type&&"comment"!==o.nodes[s].type){t=e+1;break e}}o.nodes.slice(0,t).forEach((e=>{e.remove(),"selector"===e.type?e.nodes.forEach((e=>{delete e.parent,c.append(e)})):(delete e.parent,c.append(e))}))}const i="["+encodeCSS(c.toString())+"]",l=s(c);let u=i;for(let e=0;e<l.a;e++)u+=r;const p=Math.max(1,l.b)-1;for(let e=0;e<p;e++)u+=n;for(let e=0;e<l.c;e++)u+=a;const d=e().astSync(u).nodes[0].nodes;for(let e=d.length-1;e>=0;e--)o.prepend(d[e])}));const u=l.toString();return u!==t?".js-has-pseudo "+u:t}));l.join(",")!==c.selectors.join(",")&&(t.add(c),c.cloneBefore({selectors:l}),o.preserve||c.remove())}}}}};function isWithinSupportCheck(e){let t=e.parent;for(;t;){if("atrule"===t.type&&isGuardedByAtSupportsFromAtRuleParams(t.params))return!0;t=t.parent}return!1}creator.postcss=!0;export{creator as default};
