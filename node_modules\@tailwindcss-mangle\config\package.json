{"name": "@tailwindcss-mangle/config", "type": "module", "version": "5.1.0", "description": "The config and load function of tailwindcss-mangle", "author": "ice breaker <<EMAIL>>", "license": "MIT", "homepage": "https://mangle.icebreaker.top/", "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/tailwindcss-mangle.git", "directory": "packages/config"}, "bugs": {"url": "https://github.com/sonofmagic/tailwindcss-mangle/issues"}, "keywords": ["tailwindcss", "mangle", "patch", "core", "mangle", "shared", "utils"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "dependencies": {"c12": "^2.0.4", "fs-extra": "^11.3.0", "is-css-request": "^1.0.1", "pathe": "^2.0.3", "@tailwindcss-mangle/shared": "^4.1.0"}, "scripts": {"dev": "tsup --watch --sourcemap", "build": "tsup", "test": "vitest run --coverage.enabled", "test:dev": "vitest"}}