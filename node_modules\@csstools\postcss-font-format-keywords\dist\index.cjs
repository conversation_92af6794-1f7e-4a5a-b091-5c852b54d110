"use strict";var e=require("postcss-value-parser"),t=require("@csstools/utilities");const o=["woff","truetype","opentype","woff2","embedded-opentype","collection","svg"],creator=r=>{const s="preserve"in Object(r)&&Boolean(r?.preserve);return{postcssPlugin:"postcss-font-format-keywords",Declaration(r){if("src"!==r.prop.toLowerCase())return;if(!r.value.toLowerCase().includes("format("))return;if(t.hasFallback(r))return;const a=r.parent;if(!a||"atrule"!==a.type)return;if("font-face"!==a.name.toLowerCase())return;const n=e(r.value);n.walk((t=>{"function"===t.type&&"format"===t.value.toLowerCase()&&t.nodes.forEach((t=>{"word"===t.type&&o.includes(t.value.toLowerCase())&&(t.value=e.stringify({type:"string",value:t.value,quote:'"'}))}))})),n.toString()!==r.value&&(r.cloneBefore({value:n.toString()}),s||r.remove())}}};creator.postcss=!0,module.exports=creator;
