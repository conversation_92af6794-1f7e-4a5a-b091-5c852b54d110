const SYMBOL_TABLE = {
  BACKQUOTE: "`",
  TILDE: "~",
  EXCLAM: "!",
  AT: "@",
  NUMBERSIGN: "#",
  DOLLAR: "$",
  PERCENT: "%",
  CARET: "^",
  AMPERSAND: "&",
  ASTERISK: "*",
  PARENLEFT: "(",
  PARENRIGHT: ")",
  MINUS: "-",
  UNDERSCORE: "_",
  EQUAL: "=",
  PLUS: "+",
  BRACKETLEFT: "[",
  BRACELEFT: "{",
  BRACKETRIGHT: "]",
  BRACERIGHT: "}",
  SEMICOLON: ";",
  COLON: ":",
  QUOTE: "'",
  DOUBLEQUOTE: '"',
  BACKSLASH: "\\",
  BAR: "|",
  COMMA: ",",
  LESS: "<",
  PERIOD: ".",
  GREATER: ">",
  SLASH: "/",
  QUESTION: "?",
  SPACE: " ",
  DOT: ".",
  HASH: "#"
};
const ComplexMappingChars2String = {
  "[": "_bl_",
  "]": "_br_",
  "(": "_pl_",
  ")": "_qr_",
  "#": "_h_",
  "!": "_i_",
  "/": "_s_",
  "\\": "_bs_",
  ".": "_d_",
  ":": "_c_",
  "%": "_p_",
  ",": "_co_",
  "'": "_q_",
  '"': "_dq_",
  "*": "_a_",
  "&": "_am_",
  "@": "_at_",
  "{": "_bal_",
  "}": "_bar_",
  // ' ': '_sp_',
  "+": "_plus_",
  // '-': '_m_',
  ";": "_se_",
  "<": "_l_",
  "~": "_t_",
  "=": "_e_",
  ">": "_g_",
  "?": "_qu_",
  "^": "_ca_",
  "`": "_bq_",
  "|": "_b_",
  "$": "_do_"
  // _: '_u_'
};
const ComplexMappingChars2StringEntries = Object.entries(ComplexMappingChars2String);
const MappingChars2String = {
  "[": "_",
  "]": "_",
  // for tailwindcss v4
  "(": "y",
  ")": "y",
  "{": "z",
  "}": "z",
  "+": "a",
  ",": "b",
  ":": "c",
  ".": "d",
  "=": "e",
  ";": "f",
  ">": "g",
  "#": "h",
  "!": "i",
  "@": "j",
  "^": "k",
  "<": "l",
  "*": "m",
  "&": "n",
  "?": "o",
  "%": "p",
  "'": "q",
  "$": "r",
  "/": "s",
  "~": "t",
  "|": "u",
  "`": "v",
  "\\": "w",
  '"': "x"
};
const MappingChars2StringEntries = Object.entries(MappingChars2String);
const MAX_ASCII_CHAR_CODE = 127;

function isPlainObject(value) {
  if (value === null || typeof value !== "object") {
    return false;
  }
  const prototype = Object.getPrototypeOf(value);
  if (prototype !== null && prototype !== Object.prototype && Object.getPrototypeOf(prototype) !== null) {
    return false;
  }
  if (Symbol.iterator in value) {
    return false;
  }
  if (Symbol.toStringTag in value) {
    return Object.prototype.toString.call(value) === "[object Module]";
  }
  return true;
}

function _defu(baseObject, defaults, namespace = ".", merger) {
  if (!isPlainObject(defaults)) {
    return _defu(baseObject, {}, namespace, merger);
  }
  const object = Object.assign({}, defaults);
  for (const key in baseObject) {
    if (key === "__proto__" || key === "constructor") {
      continue;
    }
    const value = baseObject[key];
    if (value === null || value === undefined) {
      continue;
    }
    if (merger && merger(object, key, value, namespace)) {
      continue;
    }
    if (Array.isArray(value) && Array.isArray(object[key])) {
      object[key] = [...value, ...object[key]];
    } else if (isPlainObject(value) && isPlainObject(object[key])) {
      object[key] = _defu(
        value,
        object[key],
        (namespace ? `${namespace}.` : "") + key.toString(),
        merger
      );
    } else {
      object[key] = value;
    }
  }
  return object;
}
function createDefu(merger) {
  return (...arguments_) => (
    // eslint-disable-next-line unicorn/no-array-reduce
    arguments_.reduce((p, c) => _defu(p, c, "", merger), {})
  );
}
const defu = createDefu();

function isAsciiNumber(code) {
  return code >= 48 && code <= 57;
}
function isAllowedClassName(className) {
  return /^[\w-]+$/.test(className);
}
function handleFirstCharacter(char, nextChar, ignoreHead) {
  if (!ignoreHead) {
    const code = char.codePointAt(0);
    if (isAsciiNumber(code)) {
      return `_${char}`;
    }
    if (char === "-" && nextChar && isAsciiNumber(nextChar.codePointAt(0))) {
      return `_${char}`;
    }
    if (char === "-" && nextChar === undefined) {
      return `_${char}`;
    }
  }
  return char;
}
function escape(selectors, options) {
  if (selectors.length === 0) {
    return "";
  }
  const { map, ignoreHead } = defu(options, {
    map: MappingChars2String,
    ignoreHead: false
  });
  const sb = [];
  for (let i = 0; i < selectors.length; i++) {
    const char = selectors[i];
    const code = char.codePointAt(0);
    if (code !== undefined) {
      if (code > MAX_ASCII_CHAR_CODE) {
        sb.push(`u${code.toString(16)}`);
      } else {
        const hit = map[char];
        if (hit) {
          sb.push(hit);
        } else if (i === 0) {
          sb.push(handleFirstCharacter(char, selectors[i + 1], ignoreHead));
        } else {
          sb.push(char);
        }
      }
    }
  }
  return sb.join("");
}

export { ComplexMappingChars2String, ComplexMappingChars2StringEntries, MAX_ASCII_CHAR_CODE, MappingChars2String, MappingChars2StringEntries, SYMBOL_TABLE, escape, isAllowedClassName, isAsciiNumber };
