{"name": "giget", "version": "1.2.5", "description": "Download templates and git repositories with pleasure!", "repository": "unjs/giget", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "bin": {"giget": "./dist/cli.mjs"}, "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest dev", "giget": "jiti ./src/cli.ts", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint . --fix && prettier -w src test", "prepack": "unbuild", "play": "pnpm giget --force-clean --verbose unjs .tmp/clone", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && vitest run --coverage"}, "dependencies": {"citty": "^0.1.6", "consola": "^3.4.0", "defu": "^6.1.4", "node-fetch-native": "^1.6.6", "nypm": "^0.5.4", "pathe": "^2.0.3", "tar": "^6.2.1"}, "devDependencies": {"@types/node": "^22.13.4", "@types/tar": "^6.1.13", "@vitest/coverage-v8": "^3.0.6", "changelogen": "^0.5.7", "eslint": "^9.20.1", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.5.1", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^3.0.6"}, "packageManager": "pnpm@10.4.1"}