"use strict";const t="inline-start",o="inline-end";var e,i;function directionFlowToAxes(t){switch(t){case e.TopToBottom:return[i.Top,i.Bottom];case e.BottomToTop:return[i.Bottom,i.Top];case e.RightToLeft:return[i.Right,i.Left];case e.LeftToRight:return[i.Left,i.Right]}}function cloneDeclaration(t,o,e){t.parent&&t.parent.some((t=>"decl"==t.type&&t.prop===e&&t.value===o))||t.cloneBefore({value:o,prop:e})}!function(t){t.TopToBottom="top-to-bottom",t.BottomToTop="bottom-to-top",t.RightToLeft="right-to-left",t.LeftToRight="left-to-right"}(e||(e={})),function(t){t.Top="top",t.Right="right",t.Bottom="bottom",t.Left="left"}(i||(i={}));const creator=i=>{const n=Object.assign({inlineDirection:e.LeftToRight},i),c=Object.values(e);if(!c.includes(n.inlineDirection))throw new Error(`[postcss-logical-float-and-clear] "inlineDirection" must be one of ${c.join(", ")}`);if(![e.LeftToRight,e.RightToLeft].includes(n.inlineDirection))return{postcssPlugin:"postcss-logical-float-and-clear",Once(){}};const[r,l]=directionFlowToAxes(n.inlineDirection),makeTransform=e=>{const i=e.value.toLowerCase();if(![t,o].includes(i))return;cloneDeclaration(e,i===t?r:l,e.prop),e.remove()};return{postcssPlugin:"postcss-logical-float-and-clear",Declaration:{float:makeTransform,clear:makeTransform}}};creator.postcss=!0,module.exports=creator;
