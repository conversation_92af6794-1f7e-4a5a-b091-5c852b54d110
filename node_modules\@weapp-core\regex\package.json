{"name": "@weapp-core/regex", "version": "1.0.1", "description": "weapp regex utils", "keywords": ["weapp", "regex", "util"], "sideEffects": false, "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "author": "SonOfMagic <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/weapp-core.git"}, "bugs": {"url": "https://github.com/sonofmagic/weapp-core/issues"}, "homepage": "https://github.com/sonofmagic/weapp-core/tree/main/packages/regex#readme", "scripts": {"build": "unbuild", "test": "vitest run", "test:dev": "vitest", "release": "pnpm publish"}}