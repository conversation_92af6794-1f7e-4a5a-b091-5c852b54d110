import e from"postcss-selector-parser";const creator=s=>{const r=Object.assign({preserve:!1},s);return{postcssPlugin:"postcss-scope-pseudo-class",prepare(){const s=new WeakSet;return{postcssPlugin:"postcss-scope-pseudo-class",Rule(t,{result:o}){if(!t.selector.toLowerCase().includes(":scope"))return;if(s.has(t))return;{let e=t.parent;for(;e;){if("atrule"===e.type&&"scope"===e.name.toLowerCase())return;e=e.parent}}let a=t.selector;try{const s=e().astSync(a);if(!s)return;s.walkPseudos((e=>{if(":has"===e.value.toLowerCase())return!1;":scope"===e.value.toLowerCase()&&(e.value=":root")})),a=s.toString()}catch(e){t.warn(o,`Failed to parse selector : "${t.selector}" with message: "${e instanceof Error?e.message:e}"`)}a!==t.selector&&(s.add(t),t.cloneBefore({selector:a}),r.preserve||t.remove())}}}}};creator.postcss=!0;export{creator as default};
