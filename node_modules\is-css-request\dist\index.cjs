"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  CSS_LANGS_RE: () => CSS_LANGS_RE,
  PostCssDialectLang: () => PostCssDialectLang,
  PreprocessLang: () => PreprocessLang,
  PureCssLang: () => PureCssLang,
  cssModuleRE: () => cssModuleRE,
  directRequestRE: () => directRequestRE,
  isCSSRequest: () => isCSSRequest,
  isDirectCSSRequest: () => isDirectCSSRequest,
  isDirectRequest: () => isDirectRequest,
  isModuleCSSRequest: () => isModuleCSSRequest
});
module.exports = __toCommonJS(src_exports);

// src/constants.ts
var CSS_LANGS_RE = /\.(css|less|sass|scss|styl|stylus|pcss|postcss|sss)(?:$|\?)/;

// src/index.ts
var cssModuleRE = new RegExp(`\\.module${CSS_LANGS_RE.source}`);
var directRequestRE = /[?&]direct\b/;
var PreprocessLang = /* @__PURE__ */ ((PreprocessLang2) => {
  PreprocessLang2["less"] = "less";
  PreprocessLang2["sass"] = "sass";
  PreprocessLang2["scss"] = "scss";
  PreprocessLang2["styl"] = "styl";
  PreprocessLang2["stylus"] = "stylus";
  return PreprocessLang2;
})(PreprocessLang || {});
var PureCssLang = /* @__PURE__ */ ((PureCssLang2) => {
  PureCssLang2["css"] = "css";
  return PureCssLang2;
})(PureCssLang || {});
var PostCssDialectLang = /* @__PURE__ */ ((PostCssDialectLang2) => {
  PostCssDialectLang2["sss"] = "sugarss";
  return PostCssDialectLang2;
})(PostCssDialectLang || {});
function isCSSRequest(request) {
  return CSS_LANGS_RE.test(request);
}
function isModuleCSSRequest(request) {
  return cssModuleRE.test(request);
}
function isDirectCSSRequest(request) {
  return CSS_LANGS_RE.test(request) && directRequestRE.test(request);
}
function isDirectRequest(request) {
  return directRequestRE.test(request);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CSS_LANGS_RE,
  PostCssDialectLang,
  PreprocessLang,
  PureCssLang,
  cssModuleRE,
  directRequestRE,
  isCSSRequest,
  isDirectCSSRequest,
  isDirectRequest,
  isModuleCSSRequest
});
