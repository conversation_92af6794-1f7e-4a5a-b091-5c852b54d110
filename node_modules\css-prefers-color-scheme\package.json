{"name": "css-prefers-color-scheme", "description": "Use light and dark color schemes in all browsers", "version": "10.0.0", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}, "./browser": {"import": "./dist/browser.mjs", "require": "./dist/browser.cjs", "default": "./dist/browser.mjs"}, "./browser-global": {"default": "./dist/browser-global.js"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"postcss": "^8.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/css-prefers-color-scheme#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "plugins/css-prefers-color-scheme"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["color", "css", "dark", "interface", "light", "media", "mode", "no-preference", "postcss", "postcss-plugin", "prefers", "queries", "query", "scheme"]}