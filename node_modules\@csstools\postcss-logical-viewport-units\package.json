{"name": "@csstools/postcss-logical-viewport-units", "description": "Use vb and vi length units in CSS", "version": "3.0.4", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/css-tokenizer": "^3.0.4", "@csstools/utilities": "^2.0.0"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-logical-viewport-units#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-logical-viewport-units"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["block", "css", "inline", "logical", "padding", "postcss", "postcss-plugin", "viewport units"]}