import '@weapp-tailwindcss/mangle';
import 'postcss';
import 'postcss-load-config';
import 'postcss-preset-env';
import 'postcss-rem-to-responsive-pixel';
export { d as CssPreflightOptions, C as CustomRuleCallback, b as IPropValue, I as IStyleHandlerOptions, a as InternalCssSelectorReplacerOptions, L as LoadedPostcssOptions, R as RequiredStyleHandlerOptions, U as UserDefinedPostcssOptions } from './types-tu70dcVo.js';
