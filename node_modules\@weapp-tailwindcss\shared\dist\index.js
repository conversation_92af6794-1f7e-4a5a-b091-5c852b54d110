"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _optionalChain(ops) { let lastAccessLHS = undefined; let value = ops[0]; let i = 1; while (i < ops.length) { const op = ops[i]; const fn = ops[i + 1]; i += 2; if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) { return undefined; } if (op === 'access' || op === 'optionalAccess') { lastAccessLHS = value; value = fn(value); } else if (op === 'call' || op === 'optionalCall') { value = fn((...args) => value.call(lastAccessLHS, ...args)); lastAccessLHS = undefined; } } return value; }



var _chunkLZVQCCFTjs = require('./chunk-LZVQCCFT.js');

// ../../node_modules/.pnpm/is-primitive@3.0.1/node_modules/is-primitive/index.js
var require_is_primitive = _chunkLZVQCCFTjs.__commonJS.call(void 0, {
  "../../node_modules/.pnpm/is-primitive@3.0.1/node_modules/is-primitive/index.js"(exports, module) {
    "use strict";
    _chunkLZVQCCFTjs.init_cjs_shims.call(void 0, );
    module.exports = function isPrimitive(val) {
      if (typeof val === "object") {
        return val === null;
      }
      return typeof val !== "function";
    };
  }
});

// ../../node_modules/.pnpm/isobject@3.0.1/node_modules/isobject/index.js
var require_isobject = _chunkLZVQCCFTjs.__commonJS.call(void 0, {
  "../../node_modules/.pnpm/isobject@3.0.1/node_modules/isobject/index.js"(exports, module) {
    "use strict";
    _chunkLZVQCCFTjs.init_cjs_shims.call(void 0, );
    module.exports = function isObject2(val) {
      return val != null && typeof val === "object" && Array.isArray(val) === false;
    };
  }
});

// ../../node_modules/.pnpm/is-plain-object@2.0.4/node_modules/is-plain-object/index.js
var require_is_plain_object = _chunkLZVQCCFTjs.__commonJS.call(void 0, {
  "../../node_modules/.pnpm/is-plain-object@2.0.4/node_modules/is-plain-object/index.js"(exports, module) {
    "use strict";
    _chunkLZVQCCFTjs.init_cjs_shims.call(void 0, );
    var isObject2 = require_isobject();
    function isObjectObject(o) {
      return isObject2(o) === true && Object.prototype.toString.call(o) === "[object Object]";
    }
    module.exports = function isPlainObject2(o) {
      var ctor, prot;
      if (isObjectObject(o) === false) return false;
      ctor = o.constructor;
      if (typeof ctor !== "function") return false;
      prot = ctor.prototype;
      if (isObjectObject(prot) === false) return false;
      if (prot.hasOwnProperty("isPrototypeOf") === false) {
        return false;
      }
      return true;
    };
  }
});

// ../../node_modules/.pnpm/set-value@4.1.0/node_modules/set-value/index.js
var require_set_value = _chunkLZVQCCFTjs.__commonJS.call(void 0, {
  "../../node_modules/.pnpm/set-value@4.1.0/node_modules/set-value/index.js"(exports, module) {
    "use strict";
    _chunkLZVQCCFTjs.init_cjs_shims.call(void 0, );
    var { deleteProperty } = Reflect;
    var isPrimitive = require_is_primitive();
    var isPlainObject2 = require_is_plain_object();
    var isObject2 = (value) => {
      return typeof value === "object" && value !== null || typeof value === "function";
    };
    var isUnsafeKey = (key) => {
      return key === "__proto__" || key === "constructor" || key === "prototype";
    };
    var validateKey = (key) => {
      if (!isPrimitive(key)) {
        throw new TypeError("Object keys must be strings or symbols");
      }
      if (isUnsafeKey(key)) {
        throw new Error(`Cannot set unsafe key: "${key}"`);
      }
    };
    var toStringKey = (input) => {
      return Array.isArray(input) ? input.flat().map(String).join(",") : input;
    };
    var createMemoKey = (input, options) => {
      if (typeof input !== "string" || !options) return input;
      let key = input + ";";
      if (options.arrays !== void 0) key += `arrays=${options.arrays};`;
      if (options.separator !== void 0) key += `separator=${options.separator};`;
      if (options.split !== void 0) key += `split=${options.split};`;
      if (options.merge !== void 0) key += `merge=${options.merge};`;
      if (options.preservePaths !== void 0) key += `preservePaths=${options.preservePaths};`;
      return key;
    };
    var memoize = (input, options, fn) => {
      const key = toStringKey(options ? createMemoKey(input, options) : input);
      validateKey(key);
      const value = setValue2.cache.get(key) || fn();
      setValue2.cache.set(key, value);
      return value;
    };
    var splitString = (input, options = {}) => {
      const sep = options.separator || ".";
      const preserve = sep === "/" ? false : options.preservePaths;
      if (typeof input === "string" && preserve !== false && /\//.test(input)) {
        return [input];
      }
      const parts = [];
      let part = "";
      const push = (part2) => {
        let number;
        if (part2.trim() !== "" && Number.isInteger(number = Number(part2))) {
          parts.push(number);
        } else {
          parts.push(part2);
        }
      };
      for (let i = 0; i < input.length; i++) {
        const value = input[i];
        if (value === "\\") {
          part += input[++i];
          continue;
        }
        if (value === sep) {
          push(part);
          part = "";
          continue;
        }
        part += value;
      }
      if (part) {
        push(part);
      }
      return parts;
    };
    var split2 = (input, options) => {
      if (options && typeof options.split === "function") return options.split(input);
      if (typeof input === "symbol") return [input];
      if (Array.isArray(input)) return input;
      return memoize(input, options, () => splitString(input, options));
    };
    var assignProp = (obj, prop, value, options) => {
      validateKey(prop);
      if (value === void 0) {
        deleteProperty(obj, prop);
      } else if (options && options.merge) {
        const merge = options.merge === "function" ? options.merge : Object.assign;
        if (merge && isPlainObject2(obj[prop]) && isPlainObject2(value)) {
          obj[prop] = merge(obj[prop], value);
        } else {
          obj[prop] = value;
        }
      } else {
        obj[prop] = value;
      }
      return obj;
    };
    var setValue2 = (target, path, value, options) => {
      if (!path || !isObject2(target)) return target;
      const keys = split2(path, options);
      let obj = target;
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const next = keys[i + 1];
        validateKey(key);
        if (next === void 0) {
          assignProp(obj, key, value, options);
          break;
        }
        if (typeof next === "number" && !Array.isArray(obj[key])) {
          obj = obj[key] = [];
          continue;
        }
        if (!isObject2(obj[key])) {
          obj[key] = {};
        }
        obj = obj[key];
      }
      return target;
    };
    setValue2.split = split2;
    setValue2.cache = /* @__PURE__ */ new Map();
    setValue2.clear = () => {
      setValue2.cache = /* @__PURE__ */ new Map();
    };
    module.exports = setValue2;
  }
});

// src/index.ts
_chunkLZVQCCFTjs.init_cjs_shims.call(void 0, );

// ../../node_modules/.pnpm/defu@6.1.4/node_modules/defu/dist/defu.mjs
_chunkLZVQCCFTjs.init_cjs_shims.call(void 0, );
function isPlainObject(value) {
  if (value === null || typeof value !== "object") {
    return false;
  }
  const prototype = Object.getPrototypeOf(value);
  if (prototype !== null && prototype !== Object.prototype && Object.getPrototypeOf(prototype) !== null) {
    return false;
  }
  if (Symbol.iterator in value) {
    return false;
  }
  if (Symbol.toStringTag in value) {
    return Object.prototype.toString.call(value) === "[object Module]";
  }
  return true;
}
function _defu(baseObject, defaults, namespace = ".", merger) {
  if (!isPlainObject(defaults)) {
    return _defu(baseObject, {}, namespace, merger);
  }
  const object = Object.assign({}, defaults);
  for (const key in baseObject) {
    if (key === "__proto__" || key === "constructor") {
      continue;
    }
    const value = baseObject[key];
    if (value === null || value === void 0) {
      continue;
    }
    if (merger && merger(object, key, value, namespace)) {
      continue;
    }
    if (Array.isArray(value) && Array.isArray(object[key])) {
      object[key] = [...value, ...object[key]];
    } else if (isPlainObject(value) && isPlainObject(object[key])) {
      object[key] = _defu(
        value,
        object[key],
        (namespace ? `${namespace}.` : "") + key.toString(),
        merger
      );
    } else {
      object[key] = value;
    }
  }
  return object;
}
function createDefu(merger) {
  return (...arguments_) => (
    // eslint-disable-next-line unicorn/no-array-reduce
    arguments_.reduce((p, c) => _defu(p, c, "", merger), {})
  );
}
var defu = createDefu();
var defuFn = createDefu((object, key, currentValue) => {
  if (object[key] !== void 0 && typeof currentValue === "function") {
    object[key] = currentValue(object[key]);
    return true;
  }
});
var defuArrayFn = createDefu((object, key, currentValue) => {
  if (Array.isArray(object[key]) && typeof currentValue === "function") {
    object[key] = currentValue(object[key]);
    return true;
  }
});

// ../../node_modules/.pnpm/get-value@4.0.1/node_modules/get-value/dist/index.mjs
_chunkLZVQCCFTjs.init_cjs_shims.call(void 0, );
var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var isObject = /* @__PURE__ */ __name((v) => v !== null && typeof v === "object", "isObject");
var join = /* @__PURE__ */ __name((segs, joinChar, options) => {
  if (typeof options.join === "function") {
    return options.join(segs);
  }
  return segs[0] + joinChar + segs[1];
}, "join");
var split = /* @__PURE__ */ __name((path, splitChar, options) => {
  if (typeof options.split === "function") {
    return options.split(path);
  }
  return path.split(splitChar);
}, "split");
var isValid = /* @__PURE__ */ __name((key, target = {}, options) => {
  if (typeof _optionalChain([options, 'optionalAccess', _ => _.isValid]) === "function") {
    return options.isValid(key, target);
  }
  return true;
}, "isValid");
var isValidObject = /* @__PURE__ */ __name((v) => {
  return isObject(v) || typeof v === "function";
}, "isValidObject");
var getValue = /* @__PURE__ */ __name((target, path, options = {}) => {
  if (!isObject(options)) {
    options = { default: options };
  }
  if (!isValidObject(target)) {
    return typeof options.default !== "undefined" ? options.default : target;
  }
  if (typeof path === "number") {
    path = String(path);
  }
  const pathIsArray = Array.isArray(path);
  const pathIsString = typeof path === "string";
  const splitChar = options.separator || ".";
  const joinChar = options.joinChar || (typeof splitChar === "string" ? splitChar : ".");
  if (!pathIsString && !pathIsArray) {
    return target;
  }
  if (target[path] !== void 0) {
    return isValid(path, target, options) ? target[path] : options.default;
  }
  const segs = pathIsArray ? path : split(path, splitChar, options);
  const len = segs.length;
  let idx = 0;
  do {
    let prop = segs[idx];
    if (typeof prop !== "string") {
      prop = String(prop);
    }
    while (prop && prop.slice(-1) === "\\") {
      prop = join([prop.slice(0, -1), segs[++idx] || ""], joinChar, options);
    }
    if (target[prop] !== void 0) {
      if (!isValid(prop, target, options)) {
        return options.default;
      }
      target = target[prop];
    } else {
      let hasProp = false;
      let n = idx + 1;
      while (n < len) {
        prop = join([prop, segs[n++]], joinChar, options);
        if (hasProp = target[prop] !== void 0) {
          if (!isValid(prop, target, options)) {
            return options.default;
          }
          target = target[prop];
          idx = n - 1;
          break;
        }
      }
      if (!hasProp) {
        return options.default;
      }
    }
  } while (++idx < len && isValidObject(target));
  if (idx === len) {
    return target;
  }
  return options.default;
}, "getValue");
var index_default = getValue;

// src/index.ts
var import_set_value = _chunkLZVQCCFTjs.__toESM.call(void 0, require_set_value());
function isRegexp(value) {
  return Object.prototype.toString.call(value) === "[object RegExp]";
}
function isMap(value) {
  return Object.prototype.toString.call(value) === "[object Map]";
}
function regExpTest(arr, str, options) {
  if (!Array.isArray(arr)) {
    throw new TypeError("paramater 'arr' should be an Array of Regexp | String");
  }
  for (const item of arr) {
    if (typeof item === "string") {
      if (_optionalChain([options, 'optionalAccess', _2 => _2.exact])) {
        if (str === item) {
          return true;
        }
      } else if (str.includes(item)) {
        return true;
      }
    } else if (isRegexp(item)) {
      item.lastIndex = 0;
      if (item.test(str)) {
        return true;
      }
    }
  }
  return false;
}
function noop() {
}
function groupBy(arr, cb) {
  if (!Array.isArray(arr)) {
    throw new TypeError("expected an array for first argument");
  }
  if (typeof cb !== "function") {
    throw new TypeError("expected a function for second argument");
  }
  const result = {};
  for (const item of arr) {
    const bucketCategory = cb(item);
    const bucket = result[bucketCategory];
    if (Array.isArray(bucket)) {
      result[bucketCategory].push(item);
    } else {
      result[bucketCategory] = [item];
    }
  }
  return result;
}
function removeExt(file) {
  if (!file) {
    return file;
  }
  return file.replace(/\.[^./]*$/, "");
}
var defuOverrideArray = createDefu((obj, key, value) => {
  if (Array.isArray(obj[key]) && Array.isArray(value)) {
    obj[key] = value;
    return true;
  }
});
var export_setValue = import_set_value.default;











exports.defu = defu; exports.defuOverrideArray = defuOverrideArray; exports.getValue = index_default; exports.groupBy = groupBy; exports.isMap = isMap; exports.isRegexp = isRegexp; exports.noop = noop; exports.regExpTest = regExpTest; exports.removeExt = removeExt; exports.setValue = export_setValue;
/*! Bundled license information:

is-primitive/index.js:
  (*!
   * is-primitive <https://github.com/jonschlinkert/is-primitive>
   *
   * Copyright (c) 2014-present, Jon Schlinkert.
   * Released under the MIT License.
   *)

isobject/index.js:
  (*!
   * isobject <https://github.com/jonschlinkert/isobject>
   *
   * Copyright (c) 2014-2017, Jon Schlinkert.
   * Released under the MIT License.
   *)

is-plain-object/index.js:
  (*!
   * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
   *
   * Copyright (c) 2014-2017, Jon Schlinkert.
   * Released under the MIT License.
   *)

set-value/index.js:
  (*!
   * set-value <https://github.com/jonschlinkert/set-value>
   *
   * Copyright (c) Jon Schlinkert (https://github.com/jonschlinkert).
   * Released under the MIT License.
   *)

get-value/dist/index.mjs:
  (*!
   * get-value <https://github.com/jonschlinkert/get-value>
   *
   * Copyright (c) 2014-present, Jon Schlinkert.
   * Released under the MIT License.
   *)
*/
