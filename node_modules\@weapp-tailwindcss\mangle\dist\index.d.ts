import { IMangleScopeContext, IMangleOptions } from './types.js';
import '@tailwindcss-mangle/shared';

declare const defaultMangleContext: IMangleScopeContext;
declare function useMangleStore(): {
    mangleContext: IMangleScopeContext;
    resetMangle: () => IMangleScopeContext;
    initMangle: (options?: boolean | IMangleOptions) => void;
    setMangleRuntimeSet: (runtimeSet: Set<string>) => void;
};

export { IMangleOptions, IMangleScopeContext, defaultMangleContext, useMangleStore };
