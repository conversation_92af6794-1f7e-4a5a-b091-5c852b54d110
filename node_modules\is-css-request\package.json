{"name": "is-css-request", "type": "module", "version": "1.0.1", "packageManager": "pnpm@9.4.0", "description": "Utils for checking if a path or url points to a CSS request.", "author": "SonOfMagic <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/sonofmagic/is-css-request#readme", "repository": {"type": "git", "url": "https://github.com/sonofmagic/is-css-request.git"}, "bugs": {"url": "https://github.com/sonofmagic/is-css-request/issues"}, "keywords": ["css", "request", "is-css", "vite", "rollup", "webpack", "esbuild"], "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./*": "./*"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "scripts": {"dev": "tsup --watch --sourcemap", "build:dev": "tsup --sourcemap", "build": "tsup", "test:dev": "vitest", "test": "vitest run", "init:rename": "tsx scripts/init/rename.ts", "init:bin": "tsx scripts/init/bin.ts", "clean": "tsx scripts/clean.ts", "ls:pack": "npm pack --dry-run", "semantic-release": "semantic-release"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "devDependencies": {"@icebreakers/eslint-config": "^0.3.9", "@tsconfig/recommended": "^1.0.6", "@types/fs-extra": "^11.0.4", "@types/klaw": "^3.0.6", "@types/lodash": "^4.17.5", "@types/lodash-es": "^4.17.12", "@types/node": "^20.14.8", "@vitest/coverage-v8": "^1.6.0", "cross-env": "^7.0.3", "defu": "^6.1.4", "del": "^7.1.0", "eslint": "9.5.0", "fs-extra": "^11.2.0", "klaw": "^4.1.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "pathe": "^1.1.2", "semantic-release": "^24.0.0", "tsup": "^8.1.0", "tsx": "^4.15.7", "typescript": "^5.5.2", "vitest": "^1.6.0"}}