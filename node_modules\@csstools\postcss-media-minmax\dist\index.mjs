import{isFunctionNode as e,isTokenNode as t,gatherNodeAncestry as r}from"@csstools/css-parser-algorithms";import{isTokenNumeric as i,TokenType as a,NumberType as n,isTokenNumber as o,isTokenDimension as s,stringify as u,isTokenWhitespace as l}from"@csstools/css-tokenizer";import{invertComparison as c,MediaFeatureEQ as d,MediaFeatureLT as f,MediaFeatureGT as m,newMediaFeaturePlain as v,matchesRatioExactly as p,matchesRatio as h,isMediaFeatureRange as g,isMediaFeature as w,isMediaFeatureRangeNameValue as y,isMediaFeatureRangeValueName as N,isMediaInParens as x,MediaInParens as b,MediaAnd as P,MediaConditionListWithAnd as L,MediaCondition as I,isMediaConditionListWithAnd as Q,isMediaAnd as S,isMediaCondition as W,isMediaQuery as O,parseCustomMedia as M,parse as T}from"@csstools/media-query-list-parser";import{mathFunctionNames as _,calcFromComponentValues as A}from"@csstools/css-calc";const C={width:"px",height:"px","device-width":"px","device-height":"px","aspect-ratio":"","device-aspect-ratio":"",color:"","color-index":"",monochrome:"",resolution:"dpi"},E={width:!1,height:!1,"device-width":!1,"device-height":!1,"aspect-ratio":!1,"device-aspect-ratio":!1,color:!0,"color-index":!0,monochrome:!0,resolution:"dpi"};function featureNamePrefix(e){return e===f.LT||e===f.LT_OR_EQ?"max-":e===m.GT||e===m.GT_OR_EQ?"min-":""}const k={">":1,"<":-1},R=.001;function transformSingleNameValuePair(r,u,l,g){let w=l.before,y=l.after;if(g||(w=l.after,y=l.before),!g){const e=c(u);if(!1===e)return;u=e}if(u===d.EQ||u===f.LT_OR_EQ||u===m.GT_OR_EQ)return Array.isArray(l.value)?v(featureNamePrefix(u)+r,...w,...l.value.flatMap((e=>e.tokens())),...y):v(featureNamePrefix(u)+r,...w,...l.value.tokens(),...y);let N,x,b=!1;if(Array.isArray(l.value)){if(!p(l.value))return;if("aspect-ratio"!==r&&"device-aspect-ratio"!==r)return;const e=h(l.value);if(-1===e)return;b=!0,N=l.value[e[0]],x=[...l.value.slice(e[0]+1).flatMap((e=>e.tokens()))]}else N=l.value,x=[];const P=C[r.toLowerCase()];if(e(N)){const e=N.getName().toLowerCase();if(_.has(e)||"env"===e){const[[e]]=A([[N]],{precision:5,toCanonicalUnits:!0});if(!(e&&t(e)&&i(e.value)&&Number.isInteger(e.value[4].value))){let e;if(void 0!==P){const t=k[u]*("px"===P?.02:R);e=[a.Dimension,`${t.toString()}${P}`,-1,-1,{value:t,unit:P,type:n.Integer}]}else if(!0===E[r]){const t=k[u];e=[a.Number,t.toString(),-1,-1,{value:t,type:n.Integer}]}else if(b){const t=k[u]*R;e=[a.Number,t.toString(),-1,-1,{value:t,type:n.Integer}]}else{const t=k[u];e=[a.Number,t.toString(),-1,-1,{value:t,type:n.Integer}]}return v(featureNamePrefix(u)+r,...w,[a.Function,"calc(",-1,-1,{value:"calc("}],[a.OpenParen,"(",-1,-1,void 0],...N.tokens().slice(1),[a.Whitespace," ",-1,-1,void 0],[a.Delim,"+",-1,-1,{value:"+"}],[a.Whitespace," ",-1,-1,void 0],e,[a.CloseParen,")",-1,-1,void 0],...x,...y)}N=e}}if(!t(N))return;let L,I=N.value,Q="";if(void 0!==P&&o(I)&&0===I[4].value)L=k[u],Q=P;else if(o(I)&&0===I[4].value)L=k[u],Q="";else if(s(I)&&0===I[4].value)L=k[u],Q=I[4].unit;else if(o(I)&&!0===E[r])L=I[4].value+k[u];else if(s(I)&&"px"===I[4].unit&&I[4].type===n.Integer)L=Number(Math.round(Number(I[4].value+.02*k[u]+"e6"))+"e-6");else{if(!s(I)&&!o(I))return;L=Number(Math.round(Number(I[4].value+R*k[u]+"e6"))+"e-6")}return Q&&(I=[a.Dimension,I[1],I[2],I[3],{value:I[4].value,unit:Q,type:I[4].type}]),I[4].value=L,s(I)?I[1]=I[4].value.toString()+I[4].unit:I[1]=I[4].value.toString(),v(featureNamePrefix(u)+r,...w,I,...x,...y)}const $=new Set(["aspect-ratio","color","color-index","device-aspect-ratio","device-height","device-width","height","horizontal-viewport-segments","monochrome","resolution","vertical-viewport-segments","width"]);function transform(e){return e.map(((e,t)=>{const i=r(e);e.walk((t=>{const r=t.node;if(!g(r))return;const n=t.parent;if(!w(n))return;const o=r.name.getName().toLowerCase();if(!$.has(o))return;if(y(r)||N(r)){const e=r.operatorKind();if(!1===e)return;const t=transformSingleNameValuePair(o,e,r.value,y(r));return void(t&&(n.feature=t.feature))}const s=i.get(n);if(!x(s))return;let u=null,l=null;{const e=r.valueOneOperatorKind();if(!1===e)return;const t=transformSingleNameValuePair(o,e,r.valueOne,!1);if(!t)return;e===f.LT||e===f.LT_OR_EQ?(u=t,u.before=n.before):(l=t,l.after=n.after)}{const e=r.valueTwoOperatorKind();if(!1===e)return;const t=transformSingleNameValuePair(o,e,r.valueTwo,!0);if(!t)return;e===f.LT||e===f.LT_OR_EQ?(l=t,l.before=n.before):(u=t,u.after=n.after)}if(!u||!l)return;const c=new b(u),d=new b(l),m=getMediaConditionListWithAndFromAncestry(s,i);if(m)return m.leading===s?(m.leading=c,void(m.list=[new P([[a.Whitespace," ",-1,-1,void 0],[a.Ident,"and",-1,-1,{value:"and"}],[a.Whitespace," ",-1,-1,void 0]],d),...m.list])):void m.list.splice(m.indexOf(i.get(s)),1,new P([[a.Whitespace," ",-1,-1,void 0],[a.Ident,"and",-1,-1,{value:"and"}],[a.Whitespace," ",-1,-1,void 0]],c),new P([[a.Whitespace," ",-1,-1,void 0],[a.Ident,"and",-1,-1,{value:"and"}],[a.Whitespace," ",-1,-1,void 0]],d));const v=new L(c,[new P([[a.Whitespace," ",-1,-1,void 0],[a.Ident,"and",-1,-1,{value:"and"}],[a.Whitespace," ",-1,-1,void 0]],d)],[[a.Whitespace," ",-1,-1,void 0]]),p=getMediaConditionInShallowMediaQueryFromAncestry(s,e,i);p?p.media=v:s.media=new I(new b(new I(v),[[a.Whitespace," ",-1,-1,void 0],[a.OpenParen,"(",-1,-1,void 0]],[[a.CloseParen,")",-1,-1,void 0]]))}));const n=e.tokens();return u(...n.filter(((e,r)=>(0!==r||0!==t||!l(e))&&!(l(e)&&n[r+1]&&l(n[r+1])))))})).join(",")}function getMediaConditionListWithAndFromAncestry(e,t){let r=e;if(r){if(r=t.get(r),Q(r))return r;if(S(r))return r=t.get(r),Q(r)?r:void 0}}function getMediaConditionInShallowMediaQueryFromAncestry(e,t,r){let i=e;if(!i)return;if(i=r.get(i),!W(i))return;const a=i;return i=r.get(i),O(i)&&i===t?a:void 0}const creator=()=>({postcssPlugin:"postcss-media-minmax",AtRule:{media(e){if(!(e.params.includes("<")||e.params.includes(">")||e.params.includes("=")))return;const t=transform(T(e.params,{preserveInvalidMediaQueries:!0,onParseError:()=>{throw e.error(`Unable to parse media query "${e.params}"`)}}));e.params!==t&&(e.params=t)},"custom-media"(e){if(!(e.params.includes("<")||e.params.includes(">")||e.params.includes("=")))return;const t=M(e.params,{preserveInvalidMediaQueries:!0,onParseError:()=>{throw e.error(`Unable to parse media query "${e.params}"`)}});if(!t||!t.mediaQueryList)return;const r=t.mediaQueryList.map((e=>e.toString())).join(","),i=transform(t.mediaQueryList);r!==i&&(e.params=`${u(...t.name)} ${i}`)}}});creator.postcss=!0;export{creator as default};
