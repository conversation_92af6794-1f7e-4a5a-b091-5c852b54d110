"use strict";var s=require("@csstools/css-calc");const e=/(?<![-\w])(?:asin|acos|atan|atan2|sin|cos|tan)\(/i,creator=t=>{const c=Object.assign({preserve:!1},t);return{postcssPlugin:"postcss-trigonometric-functions",Declaration(t){if(!e.test(t.value))return;const o=s.calc(t.value,{precision:5,toCanonicalUnits:!0});o!==t.value&&(t.cloneBefore({value:o}),c.preserve||t.remove())}}};creator.postcss=!0,module.exports=creator;
