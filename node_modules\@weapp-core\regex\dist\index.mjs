function isRegexp(value) {
  return Object.prototype.toString.call(value) === "[object RegExp]";
}
const matchAll = (regex, str) => {
  const arr = [];
  let res;
  do {
    res = regex.exec(str);
    if (res) {
      arr.push(res);
    }
  } while (res !== null);
  return arr;
};

function escapeStringRegexp(str) {
  if (typeof str !== "string") {
    throw new TypeError("Expected a string");
  }
  return str.replaceAll(/[$()*+.?[\\\]^{|}]/g, "\\$&").replaceAll("-", "\\x2d");
}
const templateClassExactRegexp = /(?<=^|\s)(?:hover-)?class=(?:["']\W+\s*\w+\()?["']([^"]+)["']/gs;
const tagWithEitherClassAndHoverClassRegexp = /<[a-z][a-z-]*[a-z]*\s+[^>]*?(?:hover-)?class="[^"]*"[^>]*?\/?>/g;
function handleRegexp(reg) {
  return `(?:${reg.source})`;
}
function getSourceString(input) {
  let result;
  if (typeof input === "string") {
    result = input;
  } else if (isRegexp(input)) {
    result = input.source;
  } else {
    result = input.toString();
  }
  return result;
}
function makePattern(arr) {
  let pattern = "";
  if (Array.isArray(arr)) {
    pattern = arr.reduce((acc, cur) => {
      if (typeof cur === "string") {
        acc.push(cur);
      } else if (isRegexp(cur)) {
        acc.push(handleRegexp(cur));
      }
      return acc;
    }, []).join("|");
  } else if (typeof arr === "string") {
    pattern = arr;
  } else if (isRegexp(arr)) {
    pattern = handleRegexp(arr);
  }
  return pattern;
}
function createTemplateHandlerMatchRegexp(tag, attrs, options = {}) {
  const { exact = true } = options;
  const prefix = exact ? "(?<=^|\\s)" : "";
  const pattern = makePattern(attrs);
  let tagPattern = getSourceString(tag);
  if (tagPattern === "*") {
    tagPattern = "[a-z][-a-z]*[a-z]*";
  }
  const source = `<(${tagPattern})\\s+[^>]*?(?:${prefix}(${pattern})="(?:[^"]*)")[^>]*?\\/?>`;
  return new RegExp(source, "g");
}
function createTemplateClassRegexp(attrs, options = {}) {
  const { exact = true } = options;
  const prefix = exact ? "(?<=^|\\s)" : "";
  const pattern = makePattern(attrs);
  const source = `(?:${prefix}${pattern})=(?:["']\\W+\\s*(?:\\w+)\\()?["']([^"]+)['"]`;
  return new RegExp(source, "gs");
}
function makeCustomAttributes(entries) {
  if (Array.isArray(entries)) {
    return entries.map(([k, v]) => {
      return {
        tagRegexp: createTemplateHandlerMatchRegexp(k, v),
        attrRegexp: createTemplateClassRegexp(v),
        tag: getSourceString(k),
        attrs: v
      };
    });
  }
}
const variableRegExp = /{{(.*?)}}/gs;
const wxsTagRegexp = /<wxs\s*(?:[a-z][a-z-]*[a-z]*(?:\s*=\s*".*?")?)*\s*>(.*?)<\/wxs>/gs;

export { createTemplateClassRegexp, createTemplateHandlerMatchRegexp, escapeStringRegexp, getSourceString, handleRegexp, isRegexp, makeCustomAttributes, makePattern, matchAll, tagWithEitherClassAndHoverClassRegexp, templateClassExactRegexp, variableRegExp, wxsTagRegexp };
