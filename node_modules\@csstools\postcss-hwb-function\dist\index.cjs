"use strict";var s=require("@csstools/postcss-progressive-custom-properties"),e=require("@csstools/css-color-parser"),t=require("@csstools/utilities"),r=require("@csstools/css-parser-algorithms"),o=require("@csstools/css-tokenizer");const a=/\bhwb\(/i,n=/^hwb$/i,basePlugin=s=>({postcssPlugin:"postcss-hwb-function",Declaration(i){const c=i.value;if(!a.test(c))return;if(t.hasFallback(i))return;if(t.hasSupportsAtRuleAncestor(i,a))return;const l=r.replaceComponentValues(r.parseCommaSeparatedListOfComponentValues(o.tokenize({css:c})),(s=>{if(!r.isFunctionNode(s)||!n.test(s.getName()))return;const t=e.color(s);return t&&!(t.syntaxFlags.has(e.SyntaxFlag.Experimental)||t.syntaxFlags.has(e.SyntaxFlag.HasNoneKeywords)||t.syntaxFlags.has(e.SyntaxFlag.RelativeColorSyntax))?e.serializeRGB(t):void 0})),u=r.stringify(l);u!==c&&(i.cloneBefore({value:u}),s?.preserve||i.remove())}});basePlugin.postcss=!0;const postcssPlugin=e=>{const t=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},e);return t.enableProgressiveCustomProperties&&t.preserve?{postcssPlugin:"postcss-hwb-function",plugins:[s(),basePlugin(t)]}:basePlugin(t)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
