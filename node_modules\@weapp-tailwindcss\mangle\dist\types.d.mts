import { IClassGeneratorOptions, ClassGenerator } from '@tailwindcss-mangle/shared';

interface IMangleOptions {
    classGenerator?: IClassGeneratorOptions;
    mangleClassFilter?: (className: string) => boolean;
}
interface IMangleScopeContext {
    rawOptions: boolean | IMangleOptions | undefined;
    runtimeSet: Set<string>;
    classGenerator: ClassGenerator;
    filter: (className: string) => boolean;
    cssHandler: (rawSource: string) => string;
    jsHandler: (rawSource: string) => string;
    wxmlHandler: (rawSource: string) => string;
}

export type { IMangleOptions, IMangleScopeContext };
