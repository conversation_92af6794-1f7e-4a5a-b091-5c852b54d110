"use strict";Object.defineProperty(exports, "__esModule", {value: true});

var _chunkLZVQCCFTjs = require('./chunk-LZVQCCFT.js');

// src/extractors/index.ts
_chunkLZVQCCFTjs.init_cjs_shims.call(void 0, );

// src/extractors/split.ts
_chunkLZVQCCFTjs.init_cjs_shims.call(void 0, );
var validateFilterRE = /[\w\u00A0-\uFFFF%-?]/;
function isValidSelector(selector = "") {
  return validateFilterRE.test(selector);
}
function splitCode(code, allowDoubleQuotes = false) {
  const splitter = allowDoubleQuotes ? /\s+/ : /\s+|"/;
  return code.split(splitter).filter((element) => isValidSelector(element));
}




exports.isValidSelector = isValidSelector; exports.splitCode = splitCode; exports.validateFilterRE = validateFilterRE;
