<template>
  <view class="h-screen bg-gray-50 flex flex-col relative">
    <!-- 顶部标题栏 -->
    <view class="relative w-full mb-3">
      <view
        class="w-full h-16 bg-primary-500 flex items-center justify-center relative"
        :style="navBarStyle"
      >
        <!-- 返回按钮 -->
        <view
          :style="navBarButtonStyle"
          @click="goBack"
          class="absolute left-0 top-0 flex items-center h-full pl-4"
        >
          <view class="w-8 h-8 flex items-center justify-center">
            <i class="fas fa-arrow-left text-white text-lg"></i>
          </view>
        </view>

        <!-- 标题 -->
        <view class="flex items-center">
          <i class="fas fa-clipboard-list text-white text-lg mr-2"></i>
          <text class="text-white text-lg font-medium">模拟考试</text>
        </view>
      </view>
    </view>

    <!-- 主体内容 -->
    <view class="flex-1 px-4 pb-16">
      <!-- 存在未完成考试的情况 -->
      <view
        v-if="hasUnfinishedExam"
        class="bg-white rounded-lg shadow p-4 mb-3"
      >
        <view class="text-center">
          <view class="mb-4">
            <i class="fas fa-clock text-orange-500 text-4xl"></i>
          </view>
          <text class="text-xl font-bold text-gray-800 mb-2 block"
            >未完成考试</text
          >
          <text class="text-gray-500 text-sm mb-6 block"
            >您有一次模拟考试尚未完成，可以选择继续作答或重新开始</text
          >

          <view class="space-y-3">
            <button
              class="w-full bg-primary-500 text-white py-3 rounded-lg font-medium flex items-center justify-center"
              @click="continueExam"
            >
              <i class="fas fa-play mr-2"></i>继续考试
            </button>
            <button
              class="w-full border border-gray-300 bg-white text-gray-600 py-3 rounded-lg font-medium flex items-center justify-center"
              @click="resetExam"
            >
              <i class="fas fa-redo mr-2"></i>重新开始
            </button>
          </view>
        </view>
      </view>
      <!-- 不存在未完成考试，显示规则设置 -->
      <view v-else class="bg-white rounded-xl shadow-sm p-4">
        <!-- 题型配置表格 -->
        <view class="mb-4">
          <!-- 表头 -->
          <view
            class="bg-primary-50 rounded-t-lg border border-primary-200 px-3 py-2"
          >
            <view class="flex items-center">
              <text class="flex-1 text-base font-medium text-primary-700"
                >题型</text
              >
              <text
                class="w-20 text-center text-base font-medium text-primary-700"
                >题库</text
              >
              <text
                class="w-24 text-center text-base font-medium text-primary-700"
                >出题数</text
              >
              <text
                class="w-24 text-center text-base font-medium text-primary-700"
                >每题分值</text
              >
            </view>
          </view>

          <!-- 表格内容 -->
          <view
            class="bg-white border-l border-r border-b border-primary-200 rounded-b-lg"
          >
            <block v-for="(rule, index) in rules" :key="rule.type">
              <view
                class="flex items-center px-3 py-3"
                :class="
                  index < rules.length - 1 ? 'border-b border-gray-100' : ''
                "
              >
                <!-- 题型名称 -->
                <text class="flex-1 text-base text-gray-800">{{
                  rule.label
                }}</text>

                <!-- 题库数量 -->
                <text class="w-20 text-center text-sm text-gray-500"
                  >{{ rule.total }}题</text
                >

                <!-- 出题数量 -->
                <view class="w-24 flex justify-center">
                  <view
                    class="flex items-center bg-gray-50 rounded border border-gray-200"
                  >
                    <button
                      @click="decreaseCount(index)"
                      class="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-minus text-xs"></i>
                    </button>
                    <input
                      type="number"
                      v-model.number="rule.count"
                      min="0"
                      :max="rule.total"
                      class="w-8 h-auto px-1 py-1 text-center text-sm border-0 bg-transparent"
                      readonly
                    />
                    <button
                      @click="increaseCount(index)"
                      class="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-plus text-xs"></i>
                    </button>
                  </view>
                </view>

                <!-- 每题分值 -->
                <view class="w-24 flex justify-center">
                  <view
                    class="flex items-center bg-gray-50 rounded border border-gray-200"
                  >
                    <button
                      @click="decreaseScore(index)"
                      class="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-minus text-xs"></i>
                    </button>
                    <input
                      type="number"
                      v-model.number="rule.score"
                      min="1"
                      class="w-8 h-auto px-1 py-1 text-center text-sm border-0 bg-transparent"
                      readonly
                    />
                    <button
                      @click="increaseScore(index)"
                      class="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-primary-500"
                    >
                      <i class="fas fa-plus text-xs"></i>
                    </button>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>

        <!-- 考试设置 -->
        <view class="space-y-2 mb-3">
          <!-- 及格分数和考试时长 -->
          <view class="flex items-center space-x-3">
            <view class="flex-1">
              <view class="flex items-center justify-between mb-2">
                <text class="text-base font-medium text-gray-800"
                  >及格分数</text
                >
                <text class="text-sm text-gray-500"
                  >总分{{ totalScore }}分</text
                >
              </view>
              <view
                class="flex items-center bg-gray-50 rounded-lg border border-gray-200 p-1"
              >
                <button
                  @click="decreasePassScore()"
                  class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                >
                  <i class="fas fa-minus text-sm"></i>
                </button>
                <input
                  type="number"
                  v-model.number="passScore"
                  :max="totalScore"
                  min="1"
                  class="flex-1 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
                  readonly
                />
                <button
                  @click="increasePassScore()"
                  class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                >
                  <i class="fas fa-plus text-sm"></i>
                </button>
              </view>
            </view>

            <view class="flex-1">
              <text class="text-base font-medium text-gray-800 mb-2 block"
                >考试时长</text
              >
              <view
                class="flex items-center bg-gray-50 rounded-lg border border-gray-200 p-1"
              >
                <button
                  @click="decreaseDuration()"
                  class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                >
                  <i class="fas fa-minus text-sm"></i>
                </button>
                <input
                  type="number"
                  v-model.number="duration"
                  min="1"
                  step="1"
                  class="flex-1 h-auto px-2 py-1 text-center text-base border-0 bg-transparent"
                  readonly
                />
                <text class="text-sm text-gray-500 mr-2">分钟</text>
                <button
                  @click="increaseDuration()"
                  class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary-500"
                >
                  <i class="fas fa-plus text-sm"></i>
                </button>
              </view>
            </view>
          </view>

          <!-- 选项乱序 -->
          <view class="flex items-center justify-between py-2">
            <text class="text-base font-medium text-gray-800">选项乱序</text>
            <view class="flex items-center">
              <switch
                :checked="shuffleOption"
                @change="(e) => (shuffleOption = e.detail.value)"
                color="#3b82f6"
                class="mr-3"
              />
              <text class="text-sm text-gray-500">{{
                shuffleOption ? "开启" : "关闭"
              }}</text>
            </view>
          </view>
        </view>

        <!-- 开始考试按钮 -->
        <button
          class="w-full bg-primary-500 text-white py-3 rounded-lg text-base font-medium flex items-center justify-center"
          @click="startExam"
        >
          <i class="fas fa-play mr-2"></i>开始考试
        </button>
      </view>
    </view>

    <!-- 右下角固定历史记录按钮 -->
    <view class="fixed bottom-6 right-6 z-50">
      <button
        class="w-12 h-12 rounded-full bg-primary-500 text-white shadow-lg flex items-center justify-center"
        @click="goHistory"
      >
        <i class="fas fa-history text-xl"></i>
      </button>
      <!-- 小红点提示（可选） -->
      <view
        v-if="hasNewHistory"
        class="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
      >
        <text class="text-white text-xs font-bold">!</text>
      </view>
    </view>
  </view>
  <!-- 继续考试弹窗 -->
  <view
    v-if="showContinueModal"
    class="fixed inset-0 z-50 flex items-center justify-center"
    @click="closeContinueModal"
  >
    <view class="absolute inset-0 bg-black bg-opacity-50"></view>
    <view
      class="bg-white rounded-lg p-6 mx-6 relative z-10 w-full max-w-sm shadow-lg"
      @click.stop
    >
      <!-- 关闭按钮 -->
      <button
        @click="closeContinueModal"
        class="absolute top-3 right-3 w-6 h-6 flex items-center justify-center"
      >
        <i class="fas fa-times text-gray-400 text-sm"></i>
      </button>

      <!-- 内容 -->
      <view class="text-center">
        <view class="mb-4">
          <i class="fas fa-clock text-orange-500 text-3xl"></i>
        </view>
        <text class="text-lg font-bold text-gray-800 block mb-2"
          >继续上次考试</text
        >
        <text class="text-gray-500 text-sm mb-6 block"
          >检测到您有一次未完成的模拟考试，是否继续作答？</text
        >
      </view>

      <!-- 操作按钮 -->
      <view class="space-y-3">
        <button
          class="w-full bg-primary-500 text-white py-3 rounded-lg font-medium flex items-center justify-center"
          @click="continueExam"
        >
          <i class="fas fa-play mr-2"></i>继续考试
        </button>
        <button
          class="w-full border border-gray-300 bg-white text-gray-600 py-3 rounded-lg font-medium flex items-center justify-center"
          @click="resetExam"
        >
          <i class="fas fa-redo mr-2"></i>重新开始
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      navBarButtonStyle: {},
      navBarStyle: {}, // 新增导航栏样式
      hasUnfinishedExam: false, // 需根据接口实际判断，默认改为false以展示规则设置
      hasNewHistory: false, // 是否有新的历史记录提示
      rules: [
        // 示例题型，实际应根据题库返回的题型和数量生成
        { type: 1, label: "单选题", total: 20, count: 10, score: 2 },
        { type: 2, label: "多选题", total: 10, count: 5, score: 3 },
        { type: 3, label: "判断题", total: 5, count: 5, score: 1 },
      ],
      passScore: 60,
      duration: 60,
      shuffleOption: true,
      showContinueModal: false, // 新增继续考试弹窗控制
      showContinueBanner: false, // 新增顶部横幅提示控制
    };
  },
  computed: {
    totalScore() {
      return this.rules.reduce((sum, r) => sum + r.count * r.score, 0);
    },
  },
  methods: {
    setNavBarButtonStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const menuButton = uni.getMenuButtonBoundingClientRect();
      style = {
        position: "absolute",
        left: menuButton.left + "px",
        top: menuButton.top + "px",
        width: menuButton.width + "px",
        height: menuButton.height + "px",
        "z-index": 20,
      };
      // #endif
      // #ifdef H5
      style = {
        position: "absolute",
        left: "16px",
        top: "16px",
        width: "40px",
        height: "40px",
        "z-index": 20,
      };
      // #endif
      this.navBarButtonStyle = style;
    },
    setNavBarStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const sys = uni.getSystemInfoSync();
      const menuButton = uni.getMenuButtonBoundingClientRect();
      const navTop = sys.statusBarHeight;
      style = {
        "padding-top": navTop + "px",
      };
      // #endif
      // #ifdef H5
      style = {
        "padding-top": "16px",
      };
      // #endif
      this.navBarStyle = style;
    },
    goBack() {
      uni.navigateBack();
    },
    // 检查是否有未完成的考试
    checkUnfinishedExam() {
      // TODO: 调用接口判断
      // this.hasUnfinishedExam = ...
    },
    continueExam() {
      // 跳转到考试页面，带上考试ID
      uni.navigateTo({ url: "/pages/practice/do-chapter?mode=mock" });
    },
    resetExam() {
      // 清除未完成考试，重置表单
      this.hasUnfinishedExam = false;
      // TODO: 调用接口清除未完成考试
    },
    startExam() {
      if (this.passScore > this.totalScore) {
        uni.showToast({ title: "及格分不能超过总分", icon: "none" });
        return;
      }
      if (this.duration < 1 || !Number.isInteger(this.duration)) {
        uni.showToast({ title: "考试时长需为正整数", icon: "none" });
        return;
      }
      // TODO: 发起考试接口，带上规则参数
      // 成功后跳转到考试页面
      uni.navigateTo({ url: "/pages/practice/do-chapter?mode=mock" });
    },
    goHistory() {
      uni.navigateTo({ url: "/pages/practice/mock-exam-history" });
    },
    // 打开继续考试弹窗
    openContinueModal() {
      this.showContinueModal = true;
    },
    // 关闭继续考试弹窗
    closeContinueModal() {
      this.showContinueModal = false;
    },
    // 打开顶部横幅提示
    openContinueBanner() {
      this.showContinueBanner = true;
    },
    // 关闭顶部横幅提示
    closeContinueBanner() {
      this.showContinueBanner = false;
    },

    // +/- 按钮方法
    decreaseCount(index) {
      if (this.rules[index].count > 0) {
        this.rules[index].count--;
      }
    },
    increaseCount(index) {
      if (this.rules[index].count < this.rules[index].total) {
        this.rules[index].count++;
      }
    },
    decreaseScore(index) {
      if (this.rules[index].score > 1) {
        this.rules[index].score--;
      }
    },
    increaseScore(index) {
      this.rules[index].score++;
    },
    decreasePassScore() {
      if (this.passScore > 1) {
        this.passScore--;
      }
    },
    increasePassScore() {
      if (this.passScore < this.totalScore) {
        this.passScore++;
      }
    },
    decreaseDuration() {
      if (this.duration > 1) {
        this.duration--;
      }
    },
    increaseDuration() {
      this.duration++;
    },
  },
  onLoad(options) {
    this.setNavBarButtonStyle();
    this.setNavBarStyle();
    if (this.hasUnfinishedExam) {
      this.openContinueBanner(); // 在onLoad时打开横幅提示
      this.openContinueModal(); // 在onLoad时打开弹窗
    }
    // TODO: 根据bankId拉取题型和数量，检查未完成考试
    // this.checkUnfinishedExam();
  },
};
</script>
