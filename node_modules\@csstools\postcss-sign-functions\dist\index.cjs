"use strict";var e=require("@csstools/css-calc"),o=require("@csstools/css-tokenizer"),n=require("@csstools/css-parser-algorithms");const s=/(?<![-\w])(?:sign|abs)\(/i,a=/(?<![-\w])(?:sign|abs)\(/i,creator=t=>{const r=Object.assign({preserve:!1},t);return{postcssPlugin:"postcss-sign-functions",Declaration(t){if(!s.test(t.value))return;let i;i=a.test(t.value)?n.replaceComponentValues(n.parseCommaSeparatedListOfComponentValues(o.tokenize({css:t.value})),replacer):n.parseCommaSeparatedListOfComponentValues(o.tokenize({css:t.value}));const p=n.stringify(e.calcFromComponentValues(i,{precision:5,toCanonicalUnits:!0}));p!==t.value&&(t.cloneBefore({value:p}),r.preserve||t.remove())}}};function replacer(e){if(!n.isFunctionNode(e))return;if("abs"!==e.getName().toLowerCase())return;const[s]=n.replaceComponentValues([e.value],replacer);return[new n.FunctionNode([o.TokenType.Function,"max(",-1,-1,{value:"max"}],[o.TokenType.CloseParen,")",-1,-1,void 0],[new n.SimpleBlockNode([o.TokenType.OpenParen,"(",-1,-1,void 0],[o.TokenType.CloseParen,")",-1,-1,void 0],n.parseListOfComponentValues(s.flatMap((e=>e.tokens())))),new n.TokenNode([o.TokenType.Comma,",",-1,-1,void 0]),new n.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new n.TokenNode([o.TokenType.Number,"-1",-1,-1,{value:-1,type:o.NumberType.Integer,signCharacter:"-"}]),new n.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new n.TokenNode([o.TokenType.Delim,"*",-1,-1,{value:"*"}]),new n.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new n.SimpleBlockNode([o.TokenType.OpenParen,"(",-1,-1,void 0],[o.TokenType.CloseParen,")",-1,-1,void 0],n.parseListOfComponentValues(s.flatMap((e=>e.tokens()))))])]}creator.postcss=!0,module.exports=creator;
