"use strict";var o;function transformAxes(o,t){const e=t?"-x":"-y",i=t?"-y":"-x",r=o.prop.toLowerCase().replace("-inline",e).replace("-block",i),s=o.value;o.parent?.some((o=>"decl"==o.type&&o.prop===r&&o.value===s))||(o.cloneBefore({prop:r,value:s}),o.remove())}!function(o){o.TopToBottom="top-to-bottom",o.BottomToTop="bottom-to-top",o.RightToLeft="right-to-left",o.LeftToRight="left-to-right"}(o||(o={}));const creator=t=>{const e=Object.assign({inlineDirection:o.LeftToRight},t);switch(e.inlineDirection){case o.LeftToRight:case o.RightToLeft:case o.TopToBottom:case o.BottomToTop:break;default:throw new Error(`[postcss-logical-viewport-units] "inlineDirection" must be one of ${Object.values(o).join(", ")}`)}const i=[o.LeftToRight,o.RightToLeft].includes(e.inlineDirection);return{postcssPlugin:"postcss-logical-overscroll-behavior",Declaration:{"overscroll-behavior-block":o=>transformAxes(o,i),"overscroll-behavior-inline":o=>transformAxes(o,i)}}};creator.postcss=!0,module.exports=creator;
