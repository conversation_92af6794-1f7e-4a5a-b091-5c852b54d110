import o from"postcss-value-parser";var t,e,i;function cloneDeclaration(o,t,e){o.parent&&o.parent.some((o=>"decl"==o.type&&o.prop===e&&o.value===t))||o.cloneBefore({value:t,prop:e})}function transformResize(t){return e=>{const{prop:i,value:n}=e,r=o(n),c=t.inlineIsHorizontal?"horizontal":"vertical",s=t.inlineIsHorizontal?"vertical":"horizontal";r.nodes.forEach((o=>{if("word"===o.type){const t=o.value.toLowerCase();if("inline"===t)return void(o.value=c);"block"===t&&(o.value=s)}}));const l=r.toString();return l!==n&&(cloneDeclaration(e,l,i),!0)}}function directionFlowToAxes(o){switch(o){case e.TopToBottom:return[i.Top,i.Bottom];case e.BottomToTop:return[i.Bottom,i.Top];case e.RightToLeft:return[i.Right,i.Left];case e.LeftToRight:return[i.Left,i.Right]}}!function(o){o.Block="block",o.Inline="inline"}(t||(t={})),function(o){o.TopToBottom="top-to-bottom",o.BottomToTop="bottom-to-top",o.RightToLeft="right-to-left",o.LeftToRight="left-to-right"}(e||(e={})),function(o){o.Top="top",o.Right="right",o.Bottom="bottom",o.Left="left"}(i||(i={}));const creator=o=>{const t=Object.assign({blockDirection:e.TopToBottom,inlineDirection:e.LeftToRight},o),n=Object.values(e);if(!n.includes(t.blockDirection))throw new Error(`[postcss-logical-resize] "blockDirection" must be one of ${n.join(", ")}`);if(!n.includes(t.inlineDirection))throw new Error(`[postcss-logical-resize] "inlineDirection" must be one of ${n.join(", ")}`);const[r,c]=directionFlowToAxes(t.blockDirection),[s,l]=directionFlowToAxes(t.inlineDirection);if(!Object.values(i).every((o=>[r,c,s,l].includes(o))))throw new Error('[postcss-logical-resize] "blockDirection" and "inlineDirection" must be on separate axes');const a={block:[r,c],inline:[s,l],inlineIsHorizontal:[e.LeftToRight,e.RightToLeft].includes(t.inlineDirection)};return{postcssPlugin:"postcss-logical-resize",Declaration:{resize:(u=transformResize(a),(o,{result:t})=>{if(!u)return;let e=!1;try{e=u(o)}catch(e){return void o.warn(t,e instanceof Error?e.message:String(e))}e&&o.remove()})}};var u};creator.postcss=!0;export{creator as default};
